from src.app.core import logger


class SelectionFields:
    def __init__(self, query_fields):
        self.query_fields = query_fields

    def extract_subfields(self, selection):
        subfield = {selection.name: []}
        for sub_selection in selection.selections:
            if sub_selection.selections:
                subfield[selection.name].append(self.extract_subfields(sub_selection))
            else:
                subfield[selection.name].append(sub_selection.name)

        return subfield

    def extract_fields(self):
        try:
            fields = []
            sub_fields = []
            for field in self.query_fields:
                for selection in field.selections:
                    if selection.selections:
                        subfield = self.extract_subfields(selection)
                        sub_fields.append(subfield)
                    else:
                        fields.append(selection.name)

            return fields, sub_fields

        except Exception as e:
            logger.error(e)
            return []
