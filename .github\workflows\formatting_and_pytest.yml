name: Test formatting and test_files
on:
  pull_request:
    branches: ["main","dev"]
    types: [opened, synchronize, reopened]

jobs:
  test-and-coverage:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12.4'
      
      - name: Cache pip packages
        id: cache-pip
        uses: actions/cache@v4
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
          restore-keys: |
            ${{ runner.os }}-pip-
      
      - name: Install system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y libpoppler-cpp-dev
      
      - name: Install dependencies
        if: steps.cache-pip.outputs.cache-hit != 'true'
        run: |
          echo "Cache not found or requirements.txt updated. Installing dependencies"
          python -m pip install --upgrade pip
          pip install -r docker/requirements.txt
      
      - name: Run pre-commit
        id: pre-commit
        run: |
          pre-commit run --all-files > pre-commit-report.txt 2>&1
        continue-on-error: true
      
      - name: Archive pre-commit results
        uses: actions/upload-artifact@v4
        with:
          name: pre-commit-report
          path: pre-commit-report.txt
      
      - name: Run pytest with coverage
        id: pytest
        run: |
          pytest --cov=./ --cov-report=xml --cov-report=html > pytest-report.txt 2>&1
        continue-on-error: true
      
      - name: Archive pytest results
        uses: actions/upload-artifact@v4
        with:
          name: pytest-report
          path: pytest-report.txt
      
      - name: Archive pytest coverage results
        uses: actions/upload-artifact@v4
        with:
          name: pytest-coverage-report
          path: |
            htmlcov/
            coverage.xml
      
      - name: Check test results
        id: check-results
        run: |
          errors=()
          
          if grep -q "failed" pre-commit-report.txt; then
            errors+=("Pre-commit checks failed. Please check the pre-commit report for details.")
          else
            echo "All pre-commit checks passed successfully!"
          fi
          
          if [ -f pytest-report.txt ]; then
            if grep -q "FAILURES\|ERROR" pytest-report.txt; then
              errors+=("Pytest failed. Please check the pytest report for details.")
            else
              echo "All pytest checks passed successfully!"
            fi
            
            if [ -f coverage.xml ]; then
              coverage_percentage=$(grep -Po '(?<=line-rate=")[^"]*' coverage.xml || echo "0")
              echo "Test coverage: $coverage_percentage"
              if (( $(echo "$coverage_percentage < 0.8" | bc -l) )); then
                errors+=("Test coverage is below 80%. Please improve your test coverage.")
              else
                echo "Test coverage is satisfactory."
              fi
            else
              errors+=("Coverage report not found. Coverage analysis may have failed.")
            fi
          else
            errors+=("Pytest report not found. Tests may have failed to run.")
          fi
          
          if [ ${#errors[@]} -ne 0 ]; then
            echo "errors=$(IFS=,; echo "${errors[*]}")" >> $GITHUB_OUTPUT
            exit 1
          fi
      
      - name: Report Errors
        if: failure()
        run: |
          echo "The following errors were found:"
          IFS=',' read -ra ERROR_ARRAY <<< "${{ steps.check-results.outputs.errors }}"
          for error in "${ERROR_ARRAY[@]}"; do
            echo "- $error"
          done
          exit 1