from enum import StrEnum
from typing import Any

from fastapi import Query
from pydantic import BaseModel, ConfigDict, Field, field_validator, model_validator

from src.app.routers.jds.schemas.create_jd_schemas import CreateJDModel
from src.app.routers.jds.schemas.jd_enums import JDStatusStrType
from src.app.routers.jds.schemas.user_schema import AssigneeSchema


class PageBaseModel(BaseModel):
    offset: int = Query(0, description="Offset")
    limit: int = Query(10, description="Limit")


class SearchQueryBaseModel(PageBaseModel):
    search_query: str | None = Query(None, max_length=255, description="Search query")


class UpdateActionEnum(StrEnum):
    JD_STATUS = "JD_STATUS"
    APPLICATION_FORM = "APPLICATION_FORM"
    WITHDRAW = "WITHDRAW"
    CAREER_PAGE = "CAREER_PAGE"


class JDPartialUpdateModel(BaseModel):
    action: UpdateActionEnum
    jd_name: str = Field("", description="JD Name")
    application_form: dict | None = Field(None, description="Application form")
    career_page: dict | None = Field(None, description="Career page")
    jd_status: JDStatusStrType | None = Field(None, description="JD Status")
    comment: str | None = Field(None, description="Comment")

    @model_validator(mode="before")
    def validate_update_action(cls, values: dict[str, Any]) -> dict[str, Any]:
        action = values.get("action")
        if action == UpdateActionEnum.APPLICATION_FORM:
            af: dict | None = values.get("application_form")
            if af is None:
                raise ValueError("application_form cannot be null for this action")

            if len(af.keys()) == 0:
                raise ValueError("application_form cannot be empty for this action")

        elif action == UpdateActionEnum.CAREER_PAGE:
            af2: dict | None = values.get("career_page")
            if af2 is None:
                raise ValueError("career_page cannot be null for this action")

            if len(af2.keys()) == 0:
                raise ValueError("career_page cannot be empty for this action")

        elif action == UpdateActionEnum.JD_STATUS:
            if values.get("jd_status") is None:
                raise ValueError("jd_status cannot be null for this action")
        elif action == UpdateActionEnum.WITHDRAW:
            af3: str | None = values.get("comment")
            if af3 is None or len(af3) < 10:  # noqa: PLR2004
                raise ValueError("comment cannot be less than 10 characters")

        return values


class JDFullUpdateModel(CreateJDModel):
    hid: str
    notice_period_in_days: int | None = None


class JDModel(CreateJDModel):
    jd_id: str
    hid: str
    company_id: int
    created_by: int
    created_by_details: AssigneeSchema | None = None
    notice_period_in_days: int | None = None
    assignees: list[AssigneeSchema] | None = None

    application_form: dict | None = Field(None, description="Application form")
    career_page: dict | None = Field(None, description="Career page")

    @field_validator("jd_id", mode="before")
    def convert_jd_id_to_str(cls, jd_id) -> str:
        if isinstance(jd_id, int):
            return str(jd_id)
        return jd_id

    model_config = ConfigDict(
        json_encoders={int: str},
    )


class JDEmbeddingModel(BaseModel):
    embedding: list[float]
    status: str
    model: str
    dimension: int


class JDSearchQueryModel(BaseModel):
    designation: str | None = Query(None, max_length=255, description="search by designation")
    skills: str | None = Query(None, description="skills must be comma separated")
    year_of_exp: int | None = Query(None, description="Search query by location")
    company_id: int

    @model_validator(mode="before")
    def validate_query(cls, values: dict[str, Any]) -> dict[str, Any]:
        if (
            values.get("designation") is None
            and values.get("skills") is None
            and values.get("location") is None
        ):
            raise ValueError("query, skills and location cannot be null")
        try:
            if values.get("skills") is not None:
                values["skills"].split(",")
        except Exception:
            raise ValueError("skills must be comma separated")
        return values


class JDStatusModel(BaseModel):
    active_jds: int
    closed_jds: int
    draft_jds: int
    archived_jds: int


class LeaderBoardModel(BaseModel):
    user_name: str
    closed_jds: int
    assigned_jds: int
