from src.app.routers.jds.repositories.abstract_jd_workflow import AbstractJDWorkflowRepo
from src.app.routers.jds.schemas.jd_workflow_schemas import CreateJDWorkflowModel
from src.app.routers.workflows.schemas.workflow_schemas import WorkflowModel


class FakeJdWorkflowRepo(AbstractJDWorkflowRepo):
    async def update_or_create(
        self, jd_id: int, data: CreateJDWorkflowModel, user_id: int
    ) -> WorkflowModel:
        return WorkflowModel(
            id=1,
            name="Sample Workflow",
            workflow={"stage": [], "version": 1},
            automations=[],
        )

    async def get_workflow_by_id(self, jd_id: int) -> WorkflowModel:
        return WorkflowModel(
            id=1,
            name="Sample Workflow",
            last_updated_by_id=1,
            workflow={
                "stage": [
                    {"automations": [1, 2, 3]},
                    {"automations": [4, 5]},
                ]
            },
            automations=[],
        )

    async def get_workflows_by_jd_ids(self, jd_ids: list[int]) -> dict[int, dict]:
        return {
            11: {
                "workflow": {"stage": [{"automations": [1, 2]}], "version": 1},
                "automations": [1, 2],
            },
            12: {"workflow": {"stage": [], "version": 1}, "automations": []},
        }
