from unittest.mock import AsyncMock, MagicMock

import pytest

from src.app.main import injector
from src.app.routers.jds.repositories.jd_repo import <PERSON><PERSON><PERSON><PERSON>
from src.app.routers.jds.repositories.jd_workflow_repo import JDWorkflowRepo
from src.app.routers.jds.schemas.apply_form_schemas import ApplyFormJDModel, JDResponse
from src.app.routers.jds.schemas.jd_enums import JobStrType, PresenceStrType, SalaryDurationStrType
from src.app.routers.jds.schemas.jd_schemas import (
    CreateJDModel,
    JDFullUpdateModel,
    JDPartialUpdateModel,
    UpdateActionEnum,
)
from src.app.routers.jds.schemas.jd_workflow_schemas import CreateJDWorkflowModel
#from src.app.routers.jds.services.jd_embedding_service import OpenAIEmbeddingsService
from src.app.routers.jds.services.jd_service import JDService
from src.app.routers.jds.services.jd_workflow_service import JDWorkflowService
from src.app.routers.workflows.schemas.workflow_schemas import WorkflowModel
from src.app.utils.repos.user_repo import UserRepository
from src.tests.fake_data.fake_jd_repo import FakeJdRepo
from src.tests.fake_data.fake_jd_workflow_repo import FakeJdWorkflowRepo
from src.tests.fake_data.fake_user_repo import FakeUserRepo
from src.tests.integration_tests.test_main import override_current_user


@pytest.fixture
def jd_service():
    injector.binder.bind(JDRepo, to=FakeJdRepo())  
    injector.binder.bind(JDService)
    return injector.get(JDService)

@pytest.fixture
def jd_workflow_service():
    injector.binder.bind(JDWorkflowRepo, to=FakeJdWorkflowRepo())  
    injector.binder.bind(UserRepository, to=FakeUserRepo())
    return injector.get(JDWorkflowService)



test_user = override_current_user()


@pytest.fixture
def jd_data():
    return CreateJDModel(
        hid="test_hid",
        jd_name="Backend Developer",
        assignee_ids=["1"],
        jd_text="test_text",
        hiring_priority="HIGH",
        designation="test_designation",
        min_work_exp=1,
        job_type="FULL_TIME",
        job_preference="ON_SITE",
        primary_skills="java, pyrthon",
        secondary_skills="multi tasking, communication",
        preferred_location="Hyderabad",
        no_of_positions=1,
        company_name="Hire10X",
        notice_period="30 days",
        salary_currency="USD",
        min_salary=50000,
        salary_duration="YEARLY",
    )


@pytest.fixture
def jd_update_data():
    return JDFullUpdateModel(
        hid="test_hid",
        jd_name="Backend Developer",
        assignee_ids=["1"],
        jd_text="test_text",
        hiring_priority="HIGH",
        designation="test_designation",
        min_work_exp=1,
        job_type="FULL_TIME",
        job_preference="ON_SITE",
        primary_skills="java, python",
        secondary_skills="multi Tasking, Communication",
        preferred_location="test_location",
        no_of_positions=1,
        company_name="test_company",
        notice_period="30 days",
        salary_currency="USD",
        min_salary=50000,
        salary_duration="YEARLY",
    )


class TestJDService:

    @pytest.mark.skip(reason="embedding issue ")
    async def test_create_jds(self, jd_service : JDService, jd_data):
        result = await jd_service.create_jd(jd_data, test_user)
        assert result is not None
        assert result.jd_name == jd_data.jd_name

    @pytest.mark.asyncio
    async def test_patch_jds_jd_status(self, jd_service : JDService):
        jd_id = 1
        jd_status = JDPartialUpdateModel(action=UpdateActionEnum.JD_STATUS, jd_status="ACTIVE")
        result = await jd_service.patch_jd(jd_id=jd_id, jd=jd_status, user=test_user)

        assert result == "Successful"

    @pytest.mark.asyncio
    async def test_patch_jds_pass_case_jd_status(self, jd_service : JDService):
        jd_id = 1
        jd = JDPartialUpdateModel(action=UpdateActionEnum.JD_STATUS, jd_status="ACTIVE")
        result = await jd_service.patch_jd(jd_id=jd_id, jd=jd, user=test_user)
        assert result == "Successful"

    @pytest.mark.asyncio
    async def test_patch_jds_application_form(self, jd_service: JDService):
        jd_id = 1
        jd_data = JDPartialUpdateModel(
            action=UpdateActionEnum.APPLICATION_FORM,
            application_form={"form_field": "Updated Form"}, 

        )
        result = await jd_service.patch_jd(jd_id, jd_data, test_user)
        assert result == "Successful"


    @pytest.mark.asyncio
    async def test_update_career_page(self, jd_service : JDService):
        jd_id = 1
        jd_data = JDPartialUpdateModel(
            action=UpdateActionEnum.CAREER_PAGE, career_page={"content": "Updated Career Page"}
        )
        result = await jd_service.patch_jd(jd_id, jd_data, test_user)
        assert result == "Successful"

    @pytest.mark.asyncio
    async def test_get_jds(self, jd_service):
        result = await jd_service.get_jds(jd_ids=[1])
        assert result == [
            JDResponse(
                jd_name="Software Engineer",
                jd_text="Software Engineer",
                designation="Software Engineer",
                min_work_exp=1,
                max_work_exp=2,
                job_type=JobStrType.FULL_TIME,
                job_preference=PresenceStrType.ON_SITE,
                primary_skills="Python, Django",
                secondary_skills="Flask, React",
                preferred_location="New York",
                company_name="Acme Inc.",
                company_url=None,
                notice_period="30 days",
                salary_currency="USD",
                min_salary=100000.0,
                max_salary=200000.0,
                salary_duration=SalaryDurationStrType.MONTHLY,
                hourly_rate=None,
                confirmed_start_date=None,
                bill_to_client=None,
                contract_acceptance=None,
                application_form={
                    "name": "John Doe",
                    "email": "<EMAIL>",
                    "phone": "1234567890",
                    "resume": "resume.pdf",
                },
                hid="123",
                jd_id="123",
                jd_status="DRAFT",
                created_by=1,
                company_id=1,
            )
        ]

    @pytest.mark.asyncio
    async def test_get_apply_form(self, jd_service):
        result = await jd_service.get_apply_form(jd_id=1)
        assert result == ApplyFormJDModel(
            jd_name="Software Engineer",
            jd_text="Design and develop software applications",
            designation="Software Engineer",
            min_work_exp=1,
            max_work_exp=5,
            job_type=JobStrType.FULL_TIME,
            job_preference=PresenceStrType.ON_SITE,
            primary_skills="Python, Django",
            secondary_skills="Flask, React",
            preferred_location="Bangalore",
            company_name="Acme Inc.",
            company_url=None,
            notice_period="30 days",
            salary_currency="USD",
            min_salary=100000.0,
            max_salary=200000.0,
            salary_duration=SalaryDurationStrType.MONTHLY,
            hourly_rate=None,
            confirmed_start_date=None,
            bill_to_client=None,
            contract_acceptance=None,
            application_form=None,
        )

    @pytest.mark.asyncio
    async def test_deletes_jd(self, jd_service :JDService):
        jd_id = 1
        result = await jd_service.delete_jd(jd_id, test_user)
        assert result == "Deleted"


class TestJDWorkflowService:
    @pytest.mark.asyncio
    async def test_get_workflow_by_id(self, jd_workflow_service: JDWorkflowService):
        jd_id = 1
        workflow_data = WorkflowModel(
            id=1,
            name="Sample Workflow",
            last_updated_by_id=1,
            workflow={
                "stage": [
                    {"automations": [1, 2, 3]},
                    {"automations": [4, 5]},
                ]
            },
            automations=[],
        )
        result = await jd_workflow_service.get_workflow_by_id(jd_id)

        assert result.id == workflow_data.id
        assert result.name == workflow_data.name 


    @pytest.mark.asyncio
    async def test_update_or_create_calls_redis_and_repo(self, monkeypatch):
        # Arrange
        jd_id = 123
        user_id = 456
        data = MagicMock(spec=CreateJDWorkflowModel)
        expected_result = MagicMock(spec=WorkflowModel)

        mock_jd_repo = MagicMock()
        mock_jd_repo.update_or_create = AsyncMock(return_value=expected_result)
        mock_automation_repo = MagicMock()
        mock_user_service = MagicMock()
        mock_redis = MagicMock()
        mock_redis.delete = AsyncMock()

        service = JDWorkflowService(
            jd_repo=mock_jd_repo,
            automation_repo=mock_automation_repo,
            user_service=mock_user_service,
            redis=mock_redis,
        )

        # Act
        result = await service.update_or_create(jd_id, data, user_id)

        # Assert
        mock_redis.delete.assert_any_await(f"jd_stage_{jd_id}")
        mock_redis.delete.assert_any_await(f"jd_stage_automation_{jd_id}")
        mock_jd_repo.update_or_create.assert_awaited_once_with(jd_id, data, user_id)
        assert result == expected_result