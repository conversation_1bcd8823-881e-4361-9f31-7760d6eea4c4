from datetime import datetime
from enum import Enum

import strawberry
from strawberry.scalars import JSON

from src.app.db.tables.jd_enums import JDStatusType, JobType, PresenceType


@strawberry.type
class AssigneeSchema:
    user_id: str
    user_name: str
    designation: str
    user_type_str: str
    email: str | None
    image_url: str | None
    created_at: str | None = None
    modified_at: str | None = None


@strawberry.type
class StageWithCount:
    stage: str
    count: int


@strawberry.enum
class JDTaskStatusEnum(Enum):
    CREATED_BY_ME = "created_by_me"
    ASSIGNED_TO_ME = "assigned_to_me"
    CREATED_BY_TEAM = "created_by_team"
    ASSIGNED_TO_TEAM = "assigned_to_team"


@strawberry.enum
class SortOrder(Enum):
    ASC = "asc"
    DESC = "desc"


@strawberry.input
class JDDateRange:
    start_date: str
    end_date: str | None = strawberry.field(default_factory=datetime.now().isoformat())


@strawberry.enum
class JDSortField(Enum):
    JD_NAME = "jd_name"
    CREATED_AT = "created_at"
    MODIFIED_AT = "modified_at"


@strawberry.input
class JDSort:
    field: JDSortField
    order: SortOrder


JobTypeEnum = strawberry.enum(JobType)
JDStatusTypeEnum = strawberry.enum(JDStatusType)
PresenceTypeEnum = strawberry.enum(PresenceType)


@strawberry.input
class JDFilters:
    jd_name: str | None = None
    assignees: list[JDTaskStatusEnum] | None = None
    status: list[JDStatusTypeEnum] | None = None  # type: ignore
    job_type: list[JobTypeEnum] | None = None  # type: ignore
    job_preference: list[PresenceTypeEnum] | None = None  # type: ignore
    location: list[str] | None = None
    date_range: JDDateRange | None = None


@strawberry.type
class JDItems:
    jd_id: str | None = None
    hid: str | None = None
    jd_name: str | None = None
    company_id: int | None = None
    designation: str | None = None
    salary_duration: str | None = None
    job_preference: str | None = None
    job_type: str | None = None
    hiring_priority: str | None = None
    department: str | None = None
    min_work_exp: float | None = None
    max_work_exp: float | None = None
    hourly_rate: str | None = None
    confirmed_start_date: str | None = None
    bill_to_client: str | None = None
    contract_acceptance: bool | None = None
    no_of_positions: int | None = None
    salary_currency: str | None = None
    min_salary: float | None = None
    max_salary: float | None = None
    primary_skills: str | None = None
    secondary_skills: str | None = None
    notice_period: str | None = None
    company_name: str | None = None
    company_url: str | None = None
    preferred_location: str | None = None
    targets_shortlisted: int | None = None
    targets_shortlisted_date: str | None = None
    targets_hiring_date: str | None = None
    recommendation_skills: str | None = None
    recommendation_keywords: str | None = None
    recommendation_companies: str | None = None
    recommendation_industries: str | None = None
    recommendation_colleges: str | None = None
    application_form: JSON | None = None
    career_page: JSON | None = None
    jd_text: str | None = None
    created_by: str | None = None
    assignees: list[AssigneeSchema] | None = None
    withdraw_assignees: list[str] | None = None
    created_at: str | None = None
    modified_at: str | None = None
    jd_status: str | None = None
    client_name: str | None = None
    stages_with_count: list[StageWithCount] | None = None
    tax_term: str | None = None
    vendor_email: str | None = None
    vendor_phone: str | None = None
    vendor_link: str | None = None

    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            if isinstance(value, Enum):
                value = value.name  # noqa: PLW2901
            elif isinstance(value, datetime):
                value = value.isoformat()  # noqa: PLW2901
            elif key in ("jd_id", "created_by"):
                value = str(value)  # noqa: PLW2901
            setattr(self, key, value)

    @strawberry.field
    def is_workflow_created(self) -> bool:
        return self.is_workflow_created > 0
