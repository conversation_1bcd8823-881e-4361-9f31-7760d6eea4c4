from datetime import datetime

from pydantic import BaseModel, Field, field_validator

from src.app.routers.jds.schemas.user_schema import AssigneeSchema


class CreateWorkflowModel(BaseModel):
    name: str = Field(..., min_length=3, max_length=255, description="Workflow name")
    workflow: dict = Field(
        ...,
        description="Workflow",
        examples=[{"stage": [{"name": "stage", "automations": [1, 2, 3]}], "version": 1}],
    )

    @field_validator("workflow")
    def validate_notice_period(cls, value):
        if "stage" not in value:
            raise ValueError("Invalid Workflow")

        stage = value["stage"]
        # check its a list
        if not isinstance(stage, list):
            raise ValueError("Invalid Workflow")

        for stage in value["stage"]:
            if "name" not in stage:
                raise ValueError("Invalid Workflow")
            if "automations" not in stage:
                raise ValueError("Invalid Workflow")

        return value


class WorkflowModel(BaseModel):
    id: int
    name: str | None = None
    workflow: dict | None = None
    automations: list | None = None
    last_updated_by: AssigneeSchema | None = None
    modified_at: datetime | None = None
    is_generated: bool = False
    last_updated_by_id: int | None = None
