from datetime import time
from enum import StrEnum
from typing import Any

from pydantic import BaseModel, Field, model_serializer


class AutomationChannelStrType(StrEnum):
    EMAIL = "EMAIL"
    SMS = "SMS"
    WHATSAPP = "WHATSAPP"
    WHATSAPP_API = "WHATSAPP_API"
    LINKEDIN = "LINKEDIN"


class AutomationEmailStrType(StrEnum):
    PERSONAL = "PERSONAL"
    PROFESSIONAL = "PROFESSIONAL"


class AutomationScheduleStrType(StrEnum):
    IMMEDIATELY = "IMMEDIATELY"
    ONTIME = "ONTIME"


class CreateAutomationModel(BaseModel):
    description: str | None = Field(None)
    template_id: int = Field(..., gt=0)
    channel_type: AutomationChannelStrType = Field(...)
    email_type: AutomationEmailStrType | None = Field(None, max_length=255)
    schedule_type: AutomationScheduleStrType = Field(..., description="schedule_type")
    schedule_time: time = Field(
        ...,
    )


class AutomationModel(CreateAutomationModel):
    automation_id: int

    @model_serializer()
    def updated_jd_id(self) -> dict[str, Any]:
        return {
            **self.__dict__,
            "schedule_time": self.schedule_time.strftime("%H:%M:%S.%fZ"),
        }
