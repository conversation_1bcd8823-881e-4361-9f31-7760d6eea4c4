import os

from fastapi_injector import attach_injector_taskiq
from injector import Injector
from redis.asyncio import Redis
from snowflakekit import Snow<PERSON><PERSON>Config, SnowflakeGenerator
from taskiq import (
    InMemoryBroker,
    SimpleRetryMiddleware,
    TaskiqEvents,
    TaskiqScheduler,
    TaskiqState,
)
from taskiq_redis import ListQueueBroker, RedisAsyncResultBackend, RedisScheduleSource
from tortoise import Tortoise

from src.app.core import logger
from src.app.core.config.sentry_config import init_sentry
from src.app.core.config.settings import get_settings
from src.app.core.config.worker_middleware import MonitoringMiddleware
from src.app.db.setup_database import TORTOISE_ORM


SERVICE_NAME = "jd"
queue_name = f"{SERVICE_NAME}_queue"
env = os.environ.get("ENVIRONMENT")
_IS_TEST = env and env == "pytest"
settings = get_settings()
redis_async_result = RedisAsyncResultBackend(
    redis_url=f"redis://{settings.REDIS_HOST}:{settings.REDIS_PORT}",
)

# Or you can use PubSubBroker if you need broadcasting
broker = ListQueueBroker(
    url=f"redis://{settings.REDIS_HOST}:{settings.REDIS_PORT}",
    queue_name=queue_name,
).with_result_backend(redis_async_result)
broker.add_middlewares(
    [
        MonitoringMiddleware(),
        SimpleRetryMiddleware(default_retry_count=3),
        # PrometheusMiddleware(server_addr="0.0.0.0", server_port=9000),
    ]
)
redis_source = RedisScheduleSource(
    url=f"redis://{settings.REDIS_HOST}:{settings.REDIS_PORT}", prefix=queue_name
)
scheduler = TaskiqScheduler(broker=broker, sources=[redis_source])
redis_client = Redis(
    host=settings.REDIS_HOST,
    port=settings.REDIS_PORT,
    # password=settings.REDIS_PASSWORD,
    # db=settings.REDIS_DB,
)

# Setup id generator
config = SnowflakeConfig(
    epoch=1609459200000,
    node_id=1,
    worker_id=1,
    time_bits=39,
    node_bits=5,
    worker_bits=8,
)

# this is for testing
if _IS_TEST:
    broker = InMemoryBroker()

# setup injection
injector = Injector()
attach_injector_taskiq(broker.state, injector=injector)

# save into injector
injector.binder.bind(SnowflakeGenerator, SnowflakeGenerator(config=config))

injector.binder.bind(Redis, redis_client)


@broker.on_event(TaskiqEvents.WORKER_STARTUP)
async def startup(state: TaskiqState) -> None:
    if _IS_TEST:
        return
    await redis_client.ping()
    await redis_source.startup()
    logger.info("Got redis connection")
    # setup database
    await Tortoise.init(config=TORTOISE_ORM)
    init_sentry(settings)


@broker.on_event(TaskiqEvents.WORKER_SHUTDOWN)
async def shutdown(state: TaskiqState) -> None:
    if _IS_TEST:
        return
    # Here we close our pool on shutdown event.
    await Tortoise.close_connections()
    await redis_client.aclose()
