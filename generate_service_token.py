#!/usr/bin/env python3
"""
Generate a service-to-service JWT token for testing internal APIs.
This token can be used to authenticate requests to endpoints that require service authentication.
"""

import jwt
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def generate_service_token():
    """Generate a JWT token for service-to-service authentication."""
    
    # Get JWT configuration from environment variables
    # These should match your application's settings
    JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY", "your-secret-key-here")
    JWT_ALGORITHM = os.getenv("JWT_ALGORITHM", "HS256")
    JWT_IDENTIFIER = os.getenv("JWT_IDENTIFIER", "hire10x-service")
    
    # Create payload with required identifier
    payload = {
        "identifier": JWT_IDENTIFIER,
        "service": "test-client",
        "purpose": "api-testing"
    }
    
    # Generate token
    token = jwt.encode(
        payload=payload,
        key=JWT_SECRET_KEY,
        algorithm=JWT_ALGORITHM
    )
    
    return token

if __name__ == "__main__":
    try:
        token = generate_service_token()
        print("🔑 Service JWT Token Generated:")
        print(f"Bearer {token}")
        print("\n📋 Copy this token and use it in the Authorization header:")
        print(f"Authorization: Bearer {token}")
        print("\n🧪 Test with curl:")
        print(f'curl -X GET "http://localhost:8080/v1/jds?jd_ids=123" \\')
        print(f'  -H "Authorization: Bearer {token}"')
        
    except Exception as e:
        print(f"❌ Error generating token: {e}")
        print("\n💡 Make sure you have the correct environment variables:")
        print("- JWT_SECRET_KEY")
        print("- JWT_ALGORITHM") 
        print("- JWT_IDENTIFIER")
