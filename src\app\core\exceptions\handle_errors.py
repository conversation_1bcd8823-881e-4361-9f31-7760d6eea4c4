from fastapi import <PERSON><PERSON><PERSON>
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException
from starlette.requests import Request
from tortoise.exceptions import (
    DoesNotExist,
    IntegrityError,
    MultipleObjectsReturned,
)

from src.app.core import logger
from src.app.core.exceptions.auth_exceptions import InvalidAuthTokenError, NoAuthTokenError
from src.app.core.exceptions.resources_exceptions import (
    InvalidOperationError,
    ResourceDuplicationError,
    ResourceNotFoundError,
)
from src.app.core.exceptions.user_exception import (
    UserAccountError,
    UserPermissionError,
)
from src.app.utils.response_helper import error_response
from src.app.utils.schemas.output_schemas import ErrorSchemas


def init_errors_handler(app: FastAPI):
    @app.exception_handler(Exception)
    async def exception_handler(request: Request, exc: Exception):
        logger.error(f"Exception: url: {request.base_url}", exc_info=exc)
        return error_response(
            request,
            error_code="INTERNAL_SERVER_ERROR",
            message=str(exc),
            status_code=500,
        )

    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        logger.error(f"HTTP exception: url: {request.base_url}", exc_info=exc)
        return error_response(
            request,
            error_code="HTTPException",
            message=str(exc.detail),
            status_code=exc.status_code,
        )

    @app.exception_handler(RequestValidationError)
    async def validation_exception_handler(request: Request, exc: RequestValidationError):
        logger.error(f"Value error exception: url: {request.base_url}", exc_info=exc)
        details = []

        for error in exc.errors():
            error_dict = {
                "loc": [str(item) for item in error.get("loc", [])],
                "msg": error.get("msg", "Unknown error"),
                "type": error.get("type", "UnknownType"),
            }
            details.append(ErrorSchemas(**error_dict))
        return error_response(
            request,
            error_code="VALIDATION_ERROR",
            message=str(exc.body),
            details=details,
            status_code=422,
        )

    @app.exception_handler(ValueError)
    async def value_exception_handler(request: Request, exc: ValueError):
        logger.error(f"Value error exception: url: {request.base_url}", exc_info=exc)
        return error_response(
            request,
            error_code="VALIDATION_ERROR",
            message=str(exc),
            status_code=422,
        )

    ########################################
    ### Tortoise exception handler here ####
    ########################################
    @app.exception_handler(DoesNotExist)
    async def not_found_exception_handler(request: Request, exc: DoesNotExist):
        logger.error(f"Not found exception: url: {request.base_url}", exc_info=exc)
        return error_response(
            request,
            error_code="RESOURCE_NOT_FOUND",
            message="Requested Resource Not Found",
            status_code=404,
        )

    @app.exception_handler(IntegrityError)
    async def integrity_exception_handler(request: Request, exc: IntegrityError):
        logger.error(f"Integrity exception: url: {request.base_url}", exc_info=exc)
        return error_response(
            request,
            error_code="INTEGRITY_ERROR",
            message=str(exc),
            status_code=400,
        )

    @app.exception_handler(MultipleObjectsReturned)
    async def multiple_object_exception_handler(request: Request, exc: MultipleObjectsReturned):
        logger.error(f"Integrity exception: url: {request.base_url}", exc_info=exc)
        return error_response(
            request,
            error_code="MULTIPLE_OBJECTS_RETURNED",
            message=str(exc),
            status_code=400,
        )

    ########################################
    ##### Custom exception handler here ####
    ########################################
    @app.exception_handler(UserAccountError)
    async def user_account_exception_handler(request: Request, exc: UserAccountError):
        logger.error(f"UserAccountError: url: {request.base_url}", exc_info=exc)
        return error_response(
            request,
            error_code=exc.error_code,
            message=exc.message,
            status_code=exc.status_code,
        )

    @app.exception_handler(UserPermissionError)
    async def user_write_exception_handler(request: Request, exc: UserPermissionError):
        logger.error(f"UserPermissionError: url: {request.base_url}", exc_info=exc)
        return error_response(
            request,
            error_code=exc.error_code,
            message=exc.message,
            status_code=exc.status_code,
        )

    @app.exception_handler(ResourceNotFoundError)
    async def resource_not_found_exception_handler(request: Request, exc: ResourceNotFoundError):
        logger.error(f"ResourceNotFoundError: url: {request.base_url}", exc_info=exc)
        return error_response(
            request,
            error_code=exc.error_code,
            message=exc.message,
            status_code=exc.status_code,
        )

    @app.exception_handler(ResourceDuplicationError)
    async def resource_duplication_exception_handler(
        request: Request, exc: ResourceDuplicationError
    ):
        logger.error(f"ResourceDuplicationError: url: {request.base_url}", exc_info=exc)
        return error_response(
            request,
            error_code=exc.error_code,
            message=exc.message,
            status_code=exc.status_code,
        )

    @app.exception_handler(InvalidOperationError)
    async def invalid_operation_exception_handler(request: Request, exc: InvalidOperationError):
        logger.error(f"InvalidOperationError: url: {request.base_url}", exc_info=exc)
        return error_response(
            request,
            error_code=exc.error_code,
            message=exc.message,
            status_code=exc.status_code,
        )

    ########################################
    ##### Auth exception handler here ######
    ########################################

    @app.exception_handler(NoAuthTokenError)
    async def no_auth_exception_handler(request: Request, exc: NoAuthTokenError):
        logger.error(f"NoAuthTokenError: url: {request.base_url}", exc_info=exc)
        return error_response(
            request,
            error_code=exc.error_code,
            message=exc.message,
            status_code=exc.status_code,
        )

    @app.exception_handler(InvalidAuthTokenError)
    async def invalid_auth_exception_handler(request: Request, exc: InvalidAuthTokenError):
        logger.error(f"InvalidAuthTokenError: url: {request.base_url}", exc_info=exc)
        return error_response(
            request,
            error_code=exc.error_code,
            message=exc.message,
            status_code=exc.status_code,
        )
