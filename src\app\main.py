from contextlib import asynccontextmanager

import firebase_admin
from fastapi import <PERSON><PERSON><PERSON>
from fastapi.responses import ORJSONResponse
from fastapi_injector import attach_injector
from firebase_admin import credentials
from injector import Injector
from redis.asyncio import Redis
from snowflakekit import <PERSON><PERSON><PERSON><PERSON>onfig, SnowflakeGenerator
from tortoise import Tortoise

from src.app.core.config.settings import get_settings
from src.app.core.config.setup_logs import init_logger
from src.app.core.config.setup_middleware import setup_middleware
from src.app.core.exceptions.handle_errors import init_errors_handler
from src.app.db.setup_database import setup_db
from src.app.routers.setup_router import init_routes

from .prometheus_config import instrumentator


settings = get_settings()

redis_client = Redis(
    host=settings.REDIS_HOST,
    port=settings.REDIS_PORT,
)


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Set up redis cache

    yield
    # close all the connections
    await Tortoise.close_connections()
    await redis_client.aclose()


app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    debug=settings.ENVIRONMENT != "PRODUCTION",
    docs_url="/docs" if settings.ENVIRONMENT.upper() != "PRODUCTION" else None,
    redoc_url="/redocs" if settings.ENVIRONMENT.upper() != "PRODUCTION" else None,
    default_response_class=ORJSONResponse,
    lifespan=lifespan,
    root_path="/jd-service",
)

setup_middleware(app)
setup_db(app)

injector = Injector()
attach_injector(app, injector=injector)

init_logger(settings.LOG_LEVEL)

# init error handler
init_errors_handler(app)

# init routes
init_routes(app)

# firebase config
cred = credentials.Certificate(settings.FIREBASE_CONFIG)
firebase_app = firebase_admin.initialize_app(cred)
# Setup id generator
config = SnowflakeConfig(
    epoch=1609459200000,
    node_id=1,
    worker_id=1,
    time_bits=39,
    node_bits=5,
    worker_bits=8,
)

injector.binder.bind(SnowflakeGenerator, SnowflakeGenerator(config=config))

injector.binder.bind(Redis, redis_client)
# Setup prometheus
# Expose metrics
instrumentator.instrument(app).expose(app, include_in_schema=False, endpoint="/metrics")
