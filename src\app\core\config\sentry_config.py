import sentry_sdk
from sentry_sdk.integrations.fastapi import FastApiIntegration

from src.app.core import Settings, logger


def init_sentry(settings: Settings):
    """
    Initialize Sentry for error tracking and performance monitoring.

    Args:
        settings (Settings): The application settings containing Sentry configuration.

    Configurations:
        - DSN: The Sentry Data Source Name (DSN) for connecting to the Sentry project.
        - Release: The application version to associate with Sentry events.
        - Environment: The environment (e.g., development, production) for Sentry events.
        - Server Name: The name of the server where the application is running.
        - Ignore Errors: A list of error types to ignore in Sentry.
        - Integrations: Sentry integrations, such as FastAPI integration.
        - Traces Sample Rate: The sample rate for tracing data (1.0 means 100%).
        - Profiles Sample Rate: The sample rate for profiling data (1.0 means 100%).
        - Send Default PII: Whether to send personally identifiable information (PII).
        - Attach Stacktrace: Whether to attach stack traces to Sentry events.

    Returns:
        None
    """
    # now setup sentry
    environment = settings.ENVIRONMENT.upper() if settings.ENVIRONMENT else ""

    if not settings.SENTRY_DSN:
        logger.warning(
            "Sentry is not configured. Sentry DSN is not set or running in local environment."
        )
        return

    allowed_environments = ["PRODUCTION", "STAGING", "DEVELOPMENT"]
    if environment not in allowed_environments:
        logger.warning(
            f"Sentry is not configured for this environment: {environment}. "
            "Allowed environments are: {allowed_environments}"
        )
        return

    logger.info(f"Sentry is configured for environment: {environment}")

    sentry_sdk.init(
        dsn=settings.SENTRY_DSN,
        release=settings.APP_VERSION,
        environment=settings.ENVIRONMENT,
        server_name=settings.SERVER_NAME,
        ignore_errors=[
            "UserAccountError",
            "HTTPException",
        ],
        integrations=[
            FastApiIntegration(
                transaction_style="endpoint",
            ),
        ],
        traces_sample_rate=1.0,
        profiles_sample_rate=1.0,
        send_default_pii=True,
        attach_stacktrace=True,
    )
