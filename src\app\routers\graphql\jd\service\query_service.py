import strawberry

from src.app.core.exceptions.user_exception import UserPermissionError
from src.app.routers.graphql.context import CustomContext
from src.app.routers.graphql.jd.schemas.jd_schema import JDFilters, JDItems, JDSort
from src.app.utils.helpers.graphql.get_fields import SelectionFields
from src.app.utils.schemas.user_schemas import RolePermissionLevelStr


@strawberry.type
class JDQuery:
    @strawberry.field
    async def get_jds(
        self,
        info: strawberry.Info,
        offset: int = 0,
        limit: int = 12,
        jd_ids: list[str] | None = None,
        filters: JDFilters | None = None,
        sort_by: JDSort | None = None,
    ) -> list[JDItems]:
        """
        Retrieves a specific job description by its ID.

        Args:
            jdId (str): The ID of the job description to retrieve.

        Returns:
            JDItem: The job description item.
        """
        context: CustomContext = info.context
        if context.user.roles_responsibilities.jd_management == RolePermissionLevelStr.NONE:
            raise UserPermissionError()
        if jd_ids:  # must be int
            jd_ids: list[int] = [int(jd_id) for jd_id in jd_ids]  # type: ignore

        fields, sub_fields = SelectionFields(info.selected_fields).extract_fields()
        result, total_records = await context.jd_service.get_jds_gql(
            jd_ids,  # type: ignore[arg-type]
            context.user,
            offset,
            limit,
            fields,
            sub_fields,
            filters,
            sort_by,
        )
        context.request.state.metadata = {"total_records": total_records}
        return result
