from abc import ABC, abstractmethod

from src.app.routers.workflows.schemas.workflow_schemas import CreateWorkflowModel, WorkflowModel
from src.app.utils.schemas import BaseQueryParams


class AbstractWorkflowRepo(ABC):
    """Abstract base class for Workflow repository."""

    @abstractmethod
    async def create_workflow(
        self,
        data: CreateWorkflowModel,
        created_by: int,
        company_id: int,
    ) -> WorkflowModel:
        """
        Creates a new workflow.

        Args:
            data: The workflow data to create.
            created_by: The ID of the user who created the workflow.
            company_id: The ID of the company that the workflow belongs to.

        Returns:
            The created workflow.

        Raises:
            NotImplementedError: This method is abstract and must be implemented.
        """
        raise NotImplementedError

    @abstractmethod
    async def check_workflow_exists(self, workflow_id: int) -> bool:
        """Checks if a workflow exists in the database.

        Args:
            workflow_id (int): The ID of the workflow to check.

        Returns:
            bool: True if the workflow exists, False otherwise.
        """
        raise NotImplementedError

    @abstractmethod
    async def get_workflow_by_id(self, workflow_id: int) -> WorkflowModel:
        """
        Gets a workflow by ID.

        Args:
            workflow_id: The ID of the workflow to get.

        Returns:
            The workflow with the given ID.

        Raises:
            NotImplementedError: This method is abstract and must be implemented.
        """
        raise NotImplementedError

    @abstractmethod
    async def get_workflows(
        self,
        query_params: BaseQueryParams,
        company_id: int,
    ) -> list[WorkflowModel]:
        """
        Gets a list of workflows.

        Args:
            query: A query string to filter the workflows.
            company_id: The ID of the company to get workflows for.
            offset: The offset into the list of workflows to return.
            limit: The maximum number of workflows to return.

        Returns:
            A list of workflows.

        Raises:
            NotImplementedError: This method is abstract and must be implemented.
        """
        raise NotImplementedError

    @abstractmethod
    async def update_workflow(
        self, workflow_id: int, company_id: int, data: CreateWorkflowModel
    ) -> bool:
        """
        Updates a workflow.

        Args:
            workflow_id: The ID of the workflow to update.
            company_id: The ID of the company that the workflow belongs to.
            data: The data to update the workflow with.

        Returns:
            True if the workflow was updated successfully, False otherwise.

        Raises:
            NotImplementedError: This method is abstract and must be implemented.
        """
        raise NotImplementedError

    @abstractmethod
    async def delete_workflow(self, workflow_id: int, company_id: int) -> bool:
        """
        Deletes a workflow.

        Args:
            workflow_id: The ID of the workflow to delete.
            company_id: The ID of the company that the workflow belongs to.

        Returns:
            True if the workflow was deleted successfully, False otherwise.

        Raises:
            NotImplementedError: This method is abstract and must be implemented.
        """
        raise NotImplementedError
