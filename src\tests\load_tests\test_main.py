from src import app
from src.app.core.auth.authentication import get_current_user
from src.app.utils.schemas.user_schemas import (
    AuthUserSchema,
    RolePermissionLevelStr,
    RoleResponsibilities,
)


def override_current_user():
    return AuthUserSchema(
        name="Test User",
        uid="1234",
        role="admin",
        company=1,
        company_name="test company",
        db_id="1",
        user_id=7,
        notation="HR",
        email="<EMAIL>",
        email_verified=True,
        iss="https://www.googleapis.com/oauth2/v3/certs",
        aud="1234",
        auth_time=0,
        roles_responsibilities=RoleResponsibilities(
            dashboard=RolePermissionLevelStr.CREATE,
            user_management=RolePermissionLevelStr.CREATE,
            jd_management=RolePermissionLevelStr.CREATE,
            candidate_management=RolePermissionLevelStr.CREATE,
            communication_module=RolePermissionLevelStr.CREATE,
            ai_assistant=RolePermissionLevelStr.CREATE,
            analytics=RolePermissionLevelStr.CREATE,
            settings=RolePermissionLevelStr.CREATE,
            credits=RolePermissionLevelStr.CREATE,
        ),
    )


app.dependency_overrides[get_current_user] = override_current_user
