name: Coverage Report
on:
  workflow_run:
    workflows: ["Test formatting and test_files"]
    types:
      - completed

jobs:
  coverage-report:
    runs-on: ubuntu-latest
    if: github.event.workflow_run.conclusion == 'success'
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12.4'
      
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install pytest pytest-cov requests
      
      - name: Get changed files
        id: changed-files
        run: |
          echo "changed_files=$(git diff --name-only ${{ github.event.workflow_run.head_sha }}~1 ${{ github.event.workflow_run.head_sha }} | wc -l)" >> $GITHUB_OUTPUT
      
      - name: Run pytest with coverage
        run: |
          pytest --cov=./ --cov-report=xml --cov-report=html
      
      - name: Generate custom coverage report
        run: |
          python generate_coverage_report.py ${{ github.run_number }} ${{ github.run_number - 1 }} ${{ steps.changed-files.outputs.changed_files }} > coverage_report.json
      
      - name: Post coverage comment
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          PR_NUMBER: ${{ github.event.workflow_run.pull_requests[0].number }}
        run: |
          python post_coverage_comment.py
      
      - name: Upload coverage reports
        uses: actions/upload-artifact@v4
        with:
          name: coverage-reports
          path: |
            coverage.xml
            htmlcov/
            coverage_report.json