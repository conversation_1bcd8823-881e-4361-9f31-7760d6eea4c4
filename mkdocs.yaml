site_name: Hire10x.ai
site_description: Next Generation Hiring Platform
theme:
  name: material
  palette:
    - scheme: default
      primary: "blue"
      accent: "deep-orange"
      toggle:
        icon: material/brightness-7
        name: Switch to dark mode

    # Palette toggle for dark mode
    - scheme: slate
      primary: "indigo"
      accent: "deep-orange"
      toggle:
        icon: material/brightness-4
        name: Switch to light mode

  font:
    text: "Roboto"

  # handle navigations
  features:
    - navigation.pagination
    - navigation.goto_top
    - navigation.path
    - navigation.tabs
    - navigation.top
    - search-suggest
    - search.highlight
    - search.share
    - content.code.copy

markdown_extensions:
  - admonition
  - pymdownx.details
  - pymdownx.superfences
  - pymdownx.highlight:
      anchor_linenums: true
      line_spans: __span
      pygments_lang_class: true
  - pymdownx.inlinehilite
  - pymdownx.snippets
  - pymdownx.superfences
  - pymdownx.superfences:
      custom_fences:
        - name: mermaid
          class: mermaid

plugins:
  - search
  - autorefs
  - blog
  - mkdocstrings:
      default_handler: python
      handlers:
        python:
          options:
            docstring_style: google
            show_inheritance: true
            show_signature: true
            show_source: true
            allow_inspection: true
            show_typehints: true
            show_toc: true
            show_bases: true
            show_inheritance_diagram: true
            parameter_headings: true
            group_by_category: true
            show_category_heading: true
            show_symbol_type_heading: true
            summary: true
            load_external_modules: true
            inherited_members: true
            show_submodules: true
            show_if_no_docstring: true
            separate_signature: true

  - gen-files:
      scripts:
        - scripts/generate_docs.py
