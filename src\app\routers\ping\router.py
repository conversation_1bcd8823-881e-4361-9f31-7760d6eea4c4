from fastapi import APIRouter, Request

from src.app.utils.response_helper import success_response
from src.app.utils.swagger_helper import generate_swagger_responses


router = APIRouter(
    tags=["Ping"],
)


@router.get("/ping", responses=generate_swagger_responses(str))
async def ping(request: Request):
    """
    Simple ping endpoint to check if the API is up and running.
    """
    return success_response("pong", request)
