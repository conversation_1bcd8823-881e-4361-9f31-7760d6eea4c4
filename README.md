# JD Service Microservice

## Table of Contents
1. [Introduction](#introduction)
2. [Project Structure](#project-structure)
3. [Setup](#setup)
   - [Prerequisites](#prerequisites)
   - [Installation](#installation)
4. [Database](#database)
   - [Configuration](#database-configuration)
   - [Migration](#database-migration)
5. [Running the Application](#running-the-application)
   - [Command Line](#command-line)
   - [VS Code](#vs-code)
6. [Development](#development)
   - [Pre-commit Hooks](#pre-commit-hooks)
   - [Code Style](#code-style)
7. [API Documentation](#api-documentation)
8. [Testing](#testing)


## Introduction
Welcome to the Hire10x JD Service microservice. This FastAPI-based application is specifically designed to handle all job-related data within the Hire10x ecosystem. As part of our microservice architecture, this service is responsible for managing job descriptions (JDs), workflows related to job postings, and providing APIs for job data manipulation and retrieval.

Key Features:
- Complete job description (JD) lifecycle management
- JD embedding generation and semantic search capabilities
- Job workflow management and tracking
- RESTful API endpoints for job data operations
- Asynchronous task processing for job-related operations
- Integration with graph databases for advanced job matching

This microservice is a crucial component of the 10XScale platform, handling all aspects of job data management. Whether you're using it for creating and storing job descriptions, searching for relevant job postings, or managing the workflow of a job through its lifecycle, the JD Service provides the necessary APIs and functionality to make this possible.

The documentation that follows will guide you through setting up your development environment, running the application, managing the database, and contributing to the project. We've designed this guide to be as comprehensive as possible, ensuring that both newcomers and experienced developers can quickly get up to speed.

Let's get started with the JD Service microservice!

## Project Structure
```
project_root/
├── src/
│   └── app/
│       ├── main.py
│       ├── worker.py
│       ├── routers/
│       │   └── jds/
│       │       ├── router.py
│       │       ├── schemas/
│       │       ├── services/
│       │       └── repositories/
│       ├── tasks/
│       │   └── jd_tasks.py
│       └── db/
│           └── setup_database.py
├── tests/
├── requirements.txt
├── .pre-commit-config.yaml
└── README.md
```

Key components:
- `routers/jds/`: Contains the API routes, schemas, services, and repositories for job description management
- `tasks/jd_tasks.py`: Background tasks for job description processing
- `worker.py`: TaskIQ worker configuration for handling asynchronous job-related tasks

## Setup

### Prerequisites
- Python 3.x
- pip
- Redis (for task queue and caching)
- PostgreSQL (for relational data storage)
- Graph Database (Neo4j recommended for job matching capabilities)
- Docker and Docker Compose (for containerized deployment)

### Installation
1. Clone the repository:
    ```bash
    git clone https://github.com/10XScale-in/backend-jd-module.git
    ```

2. Create a virtual environment and activate:
    ```bash
    python -m venv venv
    source venv/bin/activate
    ```

3. Install dependencies:
    ```bash
    pip install -r requirements.txt
    ```

## Database

### Database Configuration
The database configuration is located in `src/app/db/setup_database.py`.

### Database Migration
We use Aerich for database migrations. Follow these steps to manage your database:

1. Initialize the database initially:
    ```bash
    aerich init -t src.app.db.setup_database.TORTOISE_ORM
    ```

2. Create initial database schema:
    ```bash
    aerich init-db
    ```

3. Generate migration files:
    ```bash
    aerich migrate
    ```

4. Apply migrations:
    ```bash
    aerich upgrade
    ```

5. Revert migrations (if needed):
    ```bash
    aerich downgrade
    ```

## Running the Application

### Command Line
To run the FastAPI application using Uvicorn:
1. Start the application:
    ```bash
    uvicorn src.app.main:app --reload
    ```

2. You can also run the debugger.

### VS Code
Add the following configuration to your `.vscode/launch.json` file:
```json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python: FastAPI",
            "type": "python",
            "request": "launch",
            "module": "uvicorn",
            "args": [
                "src.app.main:app",
                "--host",
                "localhost",
                "--port",
                "8880"
            ],
            "jinja": true,
            "justMyCode": true
        }
    ]
}
```
Then you can run and debug the application using the VS Code debugger.
### Run the Broker
The JD Service uses TaskIQ for asynchronous task processing, particularly for operations like saving JD data to the graph database, generating embeddings, and other compute-intensive operations. To run the task worker:

1. Run the taskiq worker
```bash
taskiq worker src.app.worker:broker -fsd -tp 'src/**/*_tasks.py' --reload
```

This will start the worker process that handles all job-related background tasks defined in the `jd_tasks.py` file.

## Development

### Pre-commit Hooks
We use pre-commit hooks to ensure code quality. To set them up:
1. Install the pre-commit package:
    ```bash
    pip install pre-commit
    ```
2. Install the git hook scripts:
    ```bash
    pre-commit install
    ```

### Code Style
The JD Service follows strict code style guidelines to ensure consistency and quality across the codebase:

1. **Ruff**: A fast Python linter that enforces style consistency and catches common errors
2. **MyPy**: Static type checking for Python to catch type-related errors at development time
3. **Bandit**: Security-focused linter to identify potential security issues in Python code

All these tools are configured and run automatically by the pre-commit hooks.

## API Documentation
The JD Service provides a comprehensive set of API endpoints for job description management. Once the application is running, you can access the auto-generated API documentation at:

```
http://localhost:8880/docs
```

Key endpoint categories include:
- `/v1/jds`: Job description creation, retrieval, and management
- `/v1/jds/{jd_id}/embedding`: Job description embedding generation and retrieval
- `/v1/jds/search`: Search for job descriptions based on various criteria
- Workflow-related endpoints for job progression tracking

## Testing
We use pytest for testing the JD Service. To run the tests:

```bash
pytest
```

To run tests in parallel and speed up test execution:

```bash
pytest -xvs -n auto
```

For a specific test file:

```bash
pytest tests/integration_tests/test_jd_apis.py -v
```

Get all available fixtures:
```bash
pytest --fixtures
```

For detailed information on test parallel execution, refer to [this tutorial](https://www.tutorialspoint.com/pytest/pytest_run_tests_in_parallel.html).

## Scaling with KEDA
The JD Service can be scaled using KEDA (Kubernetes Event-driven Autoscaling) for production environments. For more information, refer to [KEDA documentation](https://keda.sh/).

