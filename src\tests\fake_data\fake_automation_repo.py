from datetime import time

from src.app.db.tables.workflow_tables import AutomationTable
from src.app.routers.scenarios.repositories.abstract_automation_repo import AbstractAutomationRepo
from src.app.routers.scenarios.schemas.automation_schemas import (
    AutomationChannelStrType,
    AutomationEmailStrType,
    AutomationModel,
    AutomationScheduleStrType,
    CreateAutomationModel,
)


class FakeAutomationRepo(AbstractAutomationRepo):
    async def convert_table_to_model(self, table: AutomationTable) -> AutomationModel:
        return AutomationModel(
            automation_id=1,
            description="Sample automation",
            template_id=1,
            channel_type=AutomationChannelStrType.EMAIL,
            email_type=AutomationEmailStrType.PERSONAL,
            schedule_type=AutomationScheduleStrType.IMMEDIATELY,
            schedule_time=time(8, 0, 0),
        )

    async def create_automation(
        self, data: CreateAutomationModel, company_id: int, created_by: int, scenario_id: int
    ) -> AutomationModel:
        return AutomationModel(
            automation_id=1,
            description="Sample automation",
            template_id=1,
            channel_type=AutomationChannelStrType.EMAIL,
            email_type=AutomationEmailStrType.PERSONAL,
            schedule_type=AutomationScheduleStrType.IMMEDIATELY,
            schedule_time=time(8, 0, 0),
        )

    async def get_by_scenario_id(
        self,
        scenario_id: int,
        offset: int,
        limit: int,
    ) -> list[AutomationModel]:
        return [
            AutomationModel(
                automation_id=1,
                description="Sample automation",
                template_id=1,
                channel_type=AutomationChannelStrType.EMAIL,
                email_type=AutomationEmailStrType.PERSONAL,
                schedule_type=AutomationScheduleStrType.IMMEDIATELY,
                schedule_time=time(8, 0, 0),
            )
        ]

    async def get_automation(self, automation_id: int, company_id: int) -> AutomationModel:
        return AutomationModel(
            automation_id=1,
            description="Sample automation",
            template_id=1,
            channel_type=AutomationChannelStrType.EMAIL,
            email_type=AutomationEmailStrType.PERSONAL,
            schedule_type=AutomationScheduleStrType.IMMEDIATELY,
            schedule_time=time(8, 0, 0),
        )

    async def delete_automation(self, automation_id: int, company_id: int) -> str:
        return "success"

    async def update_automation(
        self, automation_id: int, company_id: int, scenario_id: int, data: CreateAutomationModel
    ) -> AutomationModel:
        return AutomationModel(
            automation_id=1,
            description="Sample automation",
            template_id=1,
            channel_type=AutomationChannelStrType.EMAIL,
            email_type=AutomationEmailStrType.PERSONAL,
            schedule_type=AutomationScheduleStrType.IMMEDIATELY,
            schedule_time=time(8, 0, 0),
        )

    async def get_automations(
        self,
        automation_ids: list[int],
    ) -> list[AutomationModel]:
        return [
            AutomationModel(
                automation_id=1,
                description="Sample automation",
                template_id=1,
                channel_type=AutomationChannelStrType.EMAIL,
                email_type=AutomationEmailStrType.PERSONAL,
                schedule_type=AutomationScheduleStrType.IMMEDIATELY,
                schedule_time=time(8, 0, 0),
            )
        ]
