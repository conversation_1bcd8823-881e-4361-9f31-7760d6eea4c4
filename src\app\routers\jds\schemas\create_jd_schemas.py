import re
from datetime import datetime

from pydantic import BaseModel, Field, field_validator

from src.app.routers.jds.schemas.jd_enums import (
    DepartmentStrType,
    JDStatusStrType,
    JobStrType,
    PresenceStrType,
    PriorityStrType,
    SalaryDurationStrType,
)


class CreateJDModel(BaseModel):
    jd_name: str = Field(
        ...,
        min_length=5,
        max_length=255,
        description="JD name",
    )
    jd_status: JDStatusStrType = Field(JDStatusStrType.DRAFT, description="jd status")
    assignee_ids: list[str] = Field(
        ...,
        min_length=1,
        description="JD Assignees IDs",
    )
    jd_text: str = Field(
        ...,
        min_length=5,
        description="JD text",
    )
    hiring_priority: PriorityStrType = Field(
        ...,
        description="Hiring priority",
    )
    designation: str = Field(
        ...,
        min_length=3,
        max_length=255,
        description="JD Designation",
    )
    min_work_exp: int = Field(
        ...,
        ge=0,
        lt=50,
        description="min work exp",
    )
    max_work_exp: int | None = Field(
        None,
        lt=50,
        ge=0,
        description="max work exp",
    )
    job_type: JobStrType = Field(
        ...,
        description="Job Type",
    )
    job_preference: PresenceStrType = Field(
        ...,
        description="Job Preference",
    )
    primary_skills: str = Field(
        ...,
        min_length=1,
        description="List of Skills",
    )
    secondary_skills: str | None = Field(None, description="List of Secondary Skills")
    preferred_location: str = Field(
        ...,
        min_length=1,
        max_length=255,
        description="Preferred location",
    )
    no_of_positions: int = Field(
        ...,
        gt=0,
        description="No of positions",
    )
    company_name: str = Field(
        ...,
        min_length=1,
        max_length=255,
        description="Company name",
    )
    client_name: str | None = Field(
        None,
    )
    company_url: str | None = Field(
        None,
        max_length=1000,
        description="Company URL",
    )
    department: DepartmentStrType | None = Field(
        None,
        description="Department",
    )
    notice_period: str = Field(
        ...,
        min_length=1,
        max_length=255,
        description="Notice period",
    )
    salary_currency: str | None = Field(
        None,
        min_length=3,
        max_length=3,
        description="Salary currency",
    )
    min_salary: float | None = Field(
        None,
        gt=0,
        description="Minimum salary",
    )
    max_salary: float | None = Field(
        None,
        gt=0,
        description="Max salary",
    )
    salary_duration: SalaryDurationStrType | None = Field(
        None,
        description="Salary duration",
    )
    hourly_rate: str | None = Field(None, description="Hourly rate for contract type")
    confirmed_start_date: datetime | None = Field(
        None, description="Confirmed start date for contract type"
    )
    bill_to_client: str | None = Field(None, description="Bill to client for contract type")
    contract_acceptance: bool | None = Field(
        None, description="Contract acceptance for contract type"
    )
    recommendation_skills: str | None = Field(None, description="Recommendation skills")
    recommendation_keywords: str | None = Field(None, description="Recommendation keywords")
    recommendation_companies: str | None = Field(None, description="Recommendation companies")
    recommendation_industries: str | None = Field(None, description="Recommendation industries")
    recommendation_colleges: str | None = Field(None, description="Recommendation colleges")

    targets_shortlisted: int | None = Field(
        None,
        gt=0,
        description="Targets shortlisted",
    )
    targets_shortlisted_date: datetime | None = Field(
        None,
        description="Targets shortlisted date",
    )
    targets_hiring_date: datetime | None = Field(
        None,
        description="Targets hiring date",
    )

    tax_term: str | None = Field(None, max_length=100, description="Tax term for contract type")

    vendor_email: str | None = Field(None, max_length=255, description="Vendor email")
    vendor_phone: str | None = Field(None, max_length=255, description="Vendor phone")
    vendor_link: str | None = Field(None, max_length=1000, description="Vendor link")

    @field_validator("notice_period")
    def validate_notice_period(cls, value):
        if (
            len(value) < 2  # noqa: PLR2004
            or "undefined" in value.lower()
            or "null" in value.lower()
            or "none" in value.lower()
            or "nan" in value.lower()
        ):
            raise ValueError("Invalid string")

        if re.search(r"(months?|weeks?|days?|n/?a|no)", value):
            return value

        raise ValueError("Invalid Notice Periods")

    @field_validator("targets_hiring_date", "targets_shortlisted_date", "confirmed_start_date")
    def validate_date_format(cls, value):
        if value is not None and not isinstance(value, datetime):
            try:
                datetime.fromisoformat(value)
            except ValueError:
                raise ValueError("Invalid date format. Use YYYY-MM-DD (ISO) format")
        return value

    @field_validator(
        "recommendation_skills",
        "recommendation_keywords",
        "recommendation_companies",
        "recommendation_industries",
        "recommendation_colleges",
        "company_url",
        "hourly_rate",
        "bill_to_client",
        "vendor_email",
        "vendor_phone",
        "vendor_link",
        "tax_term",
    )
    def validate_string(cls, value):
        if value is not None:
            try:
                if (
                    len(value) < 1
                    or "undefined" in value.lower()
                    or "null" in value.lower()
                    or "none" in value.lower()
                    or "nan" in value.lower()
                ):
                    raise ValueError("Invalid string")

            except ValueError:
                raise ValueError("Invalid string")
        return value
