from enum import StrEnum

from fastapi import Query
from pydantic import BaseModel


class SortBy(StrEnum):
    ASC = "asc"
    DESC = "desc"


class BaseQueryParams(BaseModel):
    search_query: str | None = Query(None, description="Search query")
    offset: int = Query(0)
    limit: int = Query(10)
    sort_by: str = Query(SortBy.DESC, description="Field to sort by")
    search_field: str = Query("modified_at", description="Field to search on")
