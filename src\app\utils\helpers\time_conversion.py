import re


async def convert_to_days(duration: str) -> int | None:
    patterns = [
        (r"(\d+)\s*month(?:s)?", 30),  # Months to days
        (r"(\d+)\s*week(?:s)?", 7),  # Weeks to days
        (r"(\d+)\s*day(?:s)?", 1),
    ]

    duration = duration.lower().strip()
    for pattern, multiplier in patterns:
        match = re.search(pattern, duration)
        if match:
            return int(match.group(1)) * multiplier

    return None
