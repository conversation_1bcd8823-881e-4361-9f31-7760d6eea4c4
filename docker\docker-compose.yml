services:
  jd_app:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: jd_service
    command: ["uvicorn", "src.app.main:app", "--host", "0.0.0.0", "--port", "8082", "--reload"]
    env_file: .env
    restart: always
    volumes:
      - ..:/app
      - /home/<USER>/configs:/app/configs
    working_dir: /app
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - LOGGER_NAME=JD_SERVICE
    networks:
      - db_network
      - shared_network

  jd_worker:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: jd_taskiq_worker
    restart: always
    working_dir: /app
    command: ["taskiq", "worker", "src.app.worker:broker", "-fsd", "-tp", "src/**/*_tasks.py"]
    env_file: .env
    volumes:
      - ..:/app
      - /home/<USER>/configs:/app/configs
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - LOGGER_NAME=JD_SERVICE_TASK
    networks:
      - db_network
      - shared_network

networks:
  db_network:
    external: true
    name: db_network
  shared_network:
    external: true
    name: my_shared_network