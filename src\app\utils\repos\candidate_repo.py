from typing import Any

import httpx
import or<PERSON><PERSON>
from injector import inject, singleton
from pydantic import BaseModel
from redis.asyncio import Redis

from src.app.core.config.settings import get_settings
from src.app.utils.schemas.candidate_schema import JDStageInfoModel


@singleton
class CandidateRepository:
    """ """

    @inject
    def __init__(self, redis: Redis):
        """
        Initializes CandidateRepository.

        Args:
            redis: Client for interacting with the cache.
        """
        self.redis = redis
        self.candidate_service = get_settings().CANDIDATE_SERVICE
        self.timeout = 60 * 60

    async def fetch_jds_stage_info_from_api(self, jd_ids: list[int]) -> list[JDStageInfoModel]:
        """
        Fetches jd stage information from an external API.

        This method sends an asynchronous GET request to the candidate service API

        Args:
            jd_ids (list[int]): A list of jd IDs for which stage information is requested.

        Returns:
            list[JDStageInfoModel]-> A list of `JDStageInfoModel` instances with stage information.

            Returns an empty list if the request fails or if there is an error in the response.
        """
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(
                    f"{self.candidate_service}/v1/jds/stats",
                    params={"jd_ids": jd_ids},
                    timeout=15.0,
                )
                response.raise_for_status()
                result = response.json()

                return [JDStageInfoModel(**i) for i in result["data"]]
            except (httpx.HTTPStatusError, httpx.RequestError):
                return []

    async def invalidate_cache(self, cache_key: str):
        """Invalidates (deletes) the user cache."""
        await self.redis.delete(cache_key)

    async def update_cache(
        self, data_list: list[BaseModel | dict], cache_key_prefix: str, data_ids: list[Any]
    ):
        """
        Updates the cache with the provided data.

        This method iterates over the `data_list` and `data_ids`, creating a cache key
        for each entry by combining the `cache_key_prefix` with the corresponding `data_id`.

        Args:
            data_list (list[BaseModel | dict]): A list of data entries to store in the cache.
            cache_key_prefix (str): The prefix to use for generating cache keys.
            data_ids (list[Any]): A list of identifiers corresponding to each data entry
            in `data_list`.

        """
        for data_id, data in zip(data_ids, data_list, strict=False):
            cache_key = f"{cache_key_prefix}_{data_id}"
            await self.invalidate_cache(cache_key)  # Invalidate the old cache

            _data = data.model_dump() if isinstance(data, BaseModel) else data
            await self.redis.set(cache_key, orjson.dumps(_data), ex=self.timeout)

    async def get_cached_data_list(
        self, cache_key_prefix: str, jd_ids: list[int]
    ) -> tuple[list, list]:
        """
        Retrieves data from the cache, if available.

        Returns:
            A list of data objects from the cache, or None if not found.
        """
        found = []
        not_found = []

        for jd_id in jd_ids:
            cache_key = f"{cache_key_prefix}_{jd_id}"
            cached_data = await self.redis.get(cache_key)
            if cached_data:
                data = orjson.loads(cached_data)
                found.append(data)
            else:
                not_found.append(jd_id)

        return found, not_found

    async def update_ranking(self, jd_id: int) -> Any:
        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    f"{self.candidate_service}/v1/candidates/{jd_id}/ranking",
                    json={
                        "jd_id": str(jd_id),
                        "for_all": True,
                    },
                    timeout=60.0,
                )
                response.raise_for_status()
                result = response.json()

                return result.get("data")
            except (httpx.HTTPStatusError, httpx.RequestError):
                return []
