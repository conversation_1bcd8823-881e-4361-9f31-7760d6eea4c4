# Define service and image names
service_name="jd"
image_name="jd"

location=us-central1
project=eco-seeker-458712-i8
repository=hire10x-prod
version=release

registry_path=$location-docker.pkg.dev/$project/$repository

docker build -t $image_name:$version -f docker/Dockerfile.prod .

docker tag $image_name:$version $registry_path/$service_name:$version

docker push $registry_path/$service_name:$version
