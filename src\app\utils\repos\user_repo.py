from typing import Any

import httpx
import or<PERSON><PERSON>
from injector import inject, singleton
from redis.asyncio import Redis

from src.app.core.auth.server_to_server_auth import ServerToServerAuth
from src.app.core.config.settings import get_settings
from src.app.routers.jds.schemas.user_schema import AssigneeSchema
from src.app.utils.repos.abstract_user_repo import AbstractUserRepo


@singleton
class UserRepository(AbstractUserRepo):
    """Handles data retrieval and storage for user data."""

    @inject
    def __init__(self, redis: Redis, auth_service: ServerToServerAuth) -> None:
        """
        Initializes UserRepository.

        Args:
            redis: Client for interacting with the cache.
        """
        self.redis = redis
        self.user_service = get_settings().USER_SERVICE
        self.timeout = 60 * 60
        self.authentication = auth_service.generate_token()

    async def fetch_users_from_api(self, user_ids: list[int]) -> list[AssigneeSchema]:
        """
        Fetches a list of users from the user API.

        Returns:
            A list of AssigneeSchema objects or an empty list if the API request fails.
        """
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(
                    f"{self.user_service}/v1/users",
                    headers={"Authorization": f"Bearer {self.authentication}"},
                    params={"user_ids": user_ids},
                    timeout=15.0,
                )
                response.raise_for_status()
                users_data = response.json()
                return [AssigneeSchema(**user) for user in users_data["data"]]
            except (httpx.HTTPStatusError, httpx.RequestError):
                return []

    async def invalidate_user_cache(self, cache_key: str):
        """Invalidates (deletes) the user cache."""
        await self.redis.delete(cache_key)

    async def update_user_cache(self, users: list[AssigneeSchema]):
        """
        Updates the user cache with a new list of users.

        Args:
            users: The list of AssigneeSchema objects to store in the cache.
            cache_key: The key under which to store the users in cache.
        """
        for user in users:
            cache_key = f"user_{user.id}"
            await self.invalidate_user_cache(cache_key)
            await self.redis.set(cache_key, orjson.dumps(user.model_dump()), ex=self.timeout)

    async def get_cached_users(self, user_ids) -> tuple[list[AssigneeSchema], Any]:
        """
        Retrieves users from the cache, if available.

        Returns:
            A list of AssigneeSchema objects from the cache, or None if not found.
        """
        found_users = []
        not_found = []
        for user_id in user_ids:
            cache_key = f"user_{user_id}"
            cached_users = await self.redis.get(cache_key)
            if cached_users:
                users_data = orjson.loads(cached_users)
                found_users.append(AssigneeSchema(**users_data))
            else:
                not_found.append(int(user_id))
        return found_users, not_found
