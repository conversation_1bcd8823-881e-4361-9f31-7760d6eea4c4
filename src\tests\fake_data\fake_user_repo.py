from typing import Any
from src.app.routers.jds.schemas.user_schema import AssigneeSchema
from src.app.utils.repos.abstract_user_repo import AbstractUserRepo


class FakeUserRepo(AbstractUserRepo):
  async def fetch_users_from_api(self, user_ids: list[int]) -> list[AssigneeSchema]:
    return [
        AssigneeSchema(
            id=1,
            user_id="123",
            user_name="<PERSON>",
            designation="Software Engineer",
            user_type_str="employee",
            email="<EMAIL>",
            image_url="https://example.com/john.jpg",
            created_at="2023-01-01T00:00:00Z",
            modified_at="2023-01-01T00:00:00Z",
        )
    ]
  
  async def invalidate_user_cache(self, cache_key: str):
    return cache_key
  
  async def update_user_cache(self, users: list[AssigneeSchema]):
    return users
  
  async def get_cached_users(self, user_ids) -> tuple[list[AssigneeSchema], Any]:
    return [
        AssigneeSchema(
            id=1,
            user_id="123",
            user_name="<PERSON>",
            designation="Software Engineer",
            user_type_str="employee",
            email="<EMAIL>",
            image_url="https://example.com/john.jpg",
            created_at="2023-01-01T00:00:00Z",
            modified_at="2023-01-01T00:00:00Z",
        )
    ], []
