import pytest

from src.app.main import injector
from src.app.routers.scenarios.repositories.scenario_repo import ScenarioRepo
from src.app.routers.scenarios.schemas.scenario_schemas import CreateScenarioModel, ScenarioModel
from src.app.routers.scenarios.services.scenario_service import ScenarioService
from src.tests.fake_data.fake_scenario_repo import FakeScenarioRepo

@pytest.fixture
def scenario_service():
    injector.binder.bind(ScenarioRepo, to=FakeScenarioRepo())
    return injector.get(ScenarioService)

@pytest.mark.asyncio
async def test_create_scenario(scenario_service):
    data = CreateScenarioModel(category="test_category", description="test_description")
    company_id = 1
    created_by = 1
    result = await scenario_service.create_scenario(data, company_id, created_by)
    assert result is not None

@pytest.mark.asyncio
async def test_get_scenarios(scenario_service):
    result = await scenario_service.get_scenarios("test", 1, 0, 10)
    assert result == [
        ScenarioModel(category="Sample Category 1", description="Sample Description 1", id=1),
        ScenarioModel(category="Sample Category 2", description="Sample Description 2", id=2),
    ]

@pytest.mark.asyncio
async def test_update_scenario(scenario_service):
    data = CreateScenarioModel(category="Test", description="Test description")
    result = await scenario_service.update_scenario(scenario_id=1, data=data, company_id=1)
    assert result.id == 1
    assert result.category == "Test"
    assert result.description == "Test description"


@pytest.mark.asyncio
async def test_delete_scenario(scenario_service):
    result = await scenario_service.delete_scenario(scenario_id=999, company_id=1, created_by=1)
    assert result == "success"
