from fastapi import Request
from strawberry.fastapi import BaseContext

from src.app.routers.jds.services.jd_service import JDService
from src.app.utils.schemas.user_schemas import AuthUserSchema


class CustomContext(BaseContext):
    """Custom context class for GraphQL."""

    def __init__(self, request: Request, jd_service: JDService, user: AuthUserSchema):
        """
        Initialize the CustomContext.

        Args:
            request (Request): The current request.
            jd_service (JDService): The JDService instance.
            user (AuthUserSchema): The current user.
        """
        super().__init__()
        self.request = request
        self.jd_service = jd_service
        self.user = user
