from datetime import datetime
from enum import StrEnum

from injector import inject, singleton
from pydantic import BaseModel, Field, field_validator


class JourneyType(StrEnum):
    ASSIGNEE_UPDATE = "assignee_update"
    JD_UPDATE = "jd_update"


class CandidateJourneyModel(BaseModel):
    """Model for candidate journey"""

    journey_type: JourneyType


class NotificationType(StrEnum):
    """Centralized enum for notification types"""

    JD_CREATED = "jd_created"
    JD_UPDATED = "jd_updated"
    ASSIGNEE_UPDATE = "assignee_update"
    ASSIGNEE_CHANGE = "assignee_change"  # individual assignee notifications
    WITHDRAW_JD = "withdraw_jd"


class PublishChannel(StrEnum):
    """Centralized enum for notification channels"""

    JD = "jd"
    JD_UPDATE = "jd_update"
    BASIC = "basic"
    ACTIVITY = "activity"


class BaseNotificationData(BaseModel):
    """Base model for all notification messages"""

    title: str
    description: str
    company_id: int
    user_id: int = Field(..., description="ID of the user (32-bit int)")
    date_time: str = Field(default_factory=lambda: datetime.now().isoformat())
    meta: dict = Field(default_factory=dict, description="Additional information")
    notification_type: NotificationType


class JdNotificationData(BaseNotificationData):
    """Model containing necessary JD information"""

    id: int = Field(..., description="ID of the notification")
    parent_notification_id: int | None = Field(None, description="ID of the parent notification")
    jd_id: str = Field(..., description="ID of the job description")
    jd_name: str = Field(..., description="ID of the job description")
    hid: str | None = Field(None, description="ID of the job description")

    @field_validator("jd_id")
    def convert_jd_id_to_str(cls, value):
        if value is not None and not isinstance(value, str):
            return str(value)
        return value


class NotificationTemplate:
    """Base class for notification templates"""

    def __init__(
        self,
        title_template: str,
        description_template: str,
        channel: PublishChannel,
        data_model: type[BaseNotificationData],
    ):
        self.title_template = title_template
        self.description_template = description_template
        self.channel = channel
        self.data_model = data_model

    def format(self, **kwargs) -> dict:
        """Format the template with provided values"""
        return {
            "title": self.title_template.format(**kwargs),
            "description": self.description_template.format(**kwargs),
            **kwargs,
        }


@singleton
class NotificationCenter:
    """Centralized notification center for managing notifications"""

    @inject
    def __init__(self):
        self._templates: dict[NotificationType, NotificationTemplate] = {}
        self._register_default_templates()

    def _register_default_templates(self):
        """Register default notification templates"""

        self.register_template(
            NotificationType.JD_CREATED,
            NotificationTemplate(
                title_template="New JD {jd_name} created",
                description_template="New JD {jd_name} has been created by {user_name}.",
                channel=PublishChannel.JD_UPDATE,
                data_model=JdNotificationData,
            ),
        )

        self.register_template(
            NotificationType.JD_UPDATED,
            NotificationTemplate(
                title_template="JD {jd_name} updated",
                description_template="JD {jd_name} has been updated by {user_name}.",
                channel=PublishChannel.JD_UPDATE,
                data_model=JdNotificationData,
            ),
        )

        self.register_template(
            NotificationType.ASSIGNEE_UPDATE,
            NotificationTemplate(
                title_template="JD {jd_name} assignees updated",
                description_template="JD {jd_name} assignees updated by {user_name}.",
                channel=PublishChannel.JD_UPDATE,
                data_model=JdNotificationData,
            ),
        )

        self.register_template(
            NotificationType.ASSIGNEE_CHANGE,
            NotificationTemplate(
                title_template="You've been {action} to JD {jd_name}",
                description_template="You have been {action} to Job Description "
                "{jd_name} by {user_name}.".replace("\n", " "),
                channel=PublishChannel.JD,  # to publish fcm notification
                data_model=JdNotificationData,
            ),
        )

        self.register_template(
            NotificationType.WITHDRAW_JD,
            NotificationTemplate(
                title_template="{user_name} withdrew from JD {jd_name}",
                description_template="{user_name} has withdrawn themselves from "
                "Job Description {jd_name}. Reason: {withdraw_reason}, "
                "{jd_name}.".replace("\n", " "),
                channel=PublishChannel.JD_UPDATE,
                data_model=JdNotificationData,
            ),
        )

    def register_template(
        self, notification_type: NotificationType, template: NotificationTemplate
    ):
        """Register a template for a notification type"""
        self._templates[notification_type] = template

    def get_template(self, notification_type: NotificationType) -> NotificationTemplate:
        """Get template for a notification type"""
        if notification_type not in self._templates:
            raise ValueError(f"No template registered for {notification_type}")
        return self._templates[notification_type]

    def create_notification(self, notification_type: NotificationType, **kwargs) -> dict:
        """Create a notification using the registered template"""
        template = self.get_template(notification_type)
        data = template.format(**kwargs)
        # Add the notification type to the data
        data["notification_type"] = notification_type
        return {"data": data, "channel": template.channel, "model": template.data_model}
