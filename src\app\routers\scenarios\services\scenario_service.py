from injector import inject, singleton

from src.app.routers.scenarios.repositories.scenario_repo import ScenarioRepo
from src.app.routers.scenarios.schemas.scenario_schemas import (
    CreateScenarioModel,
    ScenarioModel,
)


@singleton
class ScenarioService:
    """
    Scenario service class for handling scenario related operations.
    """

    @inject
    def __init__(self, repo: ScenarioRepo):
        """
        Initializes the ScenarioService class.

        Args:
            repo (ScenarioRepo): An instance of ScenarioRepo for database operations.
        """
        self.repo = repo

    async def get_scenarios(
        self,
        query: str | None,
        company_id: int,
        offset: int,
        limit: int,
    ) -> list[ScenarioModel]:
        """
        Retrieves a list of scenarios based on the given parameters.

        Args:
            query (str): Search query string.
            company_id (int): ID of the company.
            offset (int): Pagination offset.
            limit (int): Pagination limit.

        Returns:
            list[ScenarioModel]: A list of scenarios matching the criteria.
        """
        return await self.repo.get_scenarios(query, company_id, offset, limit)

    async def create_scenario(
        self,
        data: CreateScenarioModel,
        company_id: int,
        created_by: int,
    ) -> ScenarioModel:
        """
        Creates a new scenario.

        Args:
            data (CreateScenarioModel): The scenario data to create.
            company_id (int): ID of the company.
            created_by (int): ID of the user creating the scenario.

        Returns:
            ScenarioModel: The created scenario.
        """
        return await self.repo.create_scenario(data, company_id, created_by)

    async def update_scenario(
        self, scenario_id: int, data: CreateScenarioModel, company_id: int
    ) -> ScenarioModel | None:
        """
        Updates an existing scenario.

        Args:
            scenario_id (int): ID of the scenario to update.
            data (CreateScenarioModel): The updated scenario data.
            company_id (int): ID of the company.

        Returns:
            ScenarioModel: The updated scenario.
        """
        return await self.repo.update_scenario(scenario_id, company_id, data)

    async def delete_scenario(self, scenario_id: int, company_id: int, created_by: int) -> str:
        """
        Deletes a scenario.

        Args:
            scenario_id (int): ID of the scenario to delete.
            company_id (int): ID of the company.
            created_by (int): ID of the user deleting the scenario.

        Returns:
            str: "success" if the deletion was successful, "failed" otherwise.
        """
        res = await self.repo.delete_scenario(scenario_id, company_id, created_by=created_by)
        return "success" if res else "failed"
