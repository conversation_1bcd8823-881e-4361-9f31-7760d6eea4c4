# 1. Send Notification Based when J<PERSON> is assigned
# 2. Send Notification Based when JD is unassigned
# 3. Send Notification Based when JD status Changed
# 4. Send Notification with workflow (in workflow if email is used)
#     then we need to check is email setup in user account
#     if not then send notification to the assignee user


# JD Module
# CV Module
# User Module
# Communication Module
# Ranking Module
# AI Module

# Credits & Contact Module
# Vendor DB Module

# 4afece2gHA0


# Friday 14th March (Target):
# 1. Vendor Onboarding
# 2. Login & Registration
# 3. Create JD
# 4. Import Candidate From Linkedin and AI Search and Application Form
# 5. Ranking
# 6. Email Communication (Optional)
