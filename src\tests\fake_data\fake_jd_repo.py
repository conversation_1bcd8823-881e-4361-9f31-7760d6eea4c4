from src.app.routers.graphql.jd.schemas.jd_schema import JDFilters, JDSort, JDTaskStatusEnum
from src.app.routers.jds.repositories.abstract_jd_repo import AbstractJDRepo
from src.app.routers.jds.schemas.apply_form_schemas import (
    ApplyFormJDModel,
    JDResponse,
    JDSearchResponseModel,
)
from src.app.routers.jds.schemas.create_jd_schemas import CreateJDModel
from src.app.routers.jds.schemas.jd_schemas import (
    JDFullUpdateModel,
    JDModel,
    JDPartialUpdateModel,
    JDSearchQueryModel,
    JDStatusModel,
    LeaderBoardModel,
)
from src.app.utils.schemas.user_schemas import AuthUserSchema


class FakeJdRepo(AbstractJDRepo):
    async def get_jd_count_today(self, company_id: int) -> int:
        return 2

    async def convert_to_table(self, jd: JDModel) -> dict:
        return {
            "jd_id": "123",
            "hid": "HID001",
            "company_id": 456,
            "created_by": 789,
            "created_by_details": {"assignee_id": "A123", "assignee_name": "John Doe"},
            "notice_period_in_days": 30,
            "assignees": [{"assignee_id": "A123", "assignee_name": "John Doe"}],
            "application_form": {"field1": "value1", "field2": "value2"},
            "career_page": {"field1": "value1", "field2": "value2"},
        }

    async def check_jd_user_access(self, jd_id: int, user_id: int) -> bool:
        return True

    async def is_ready_to_save(self, jd_data: CreateJDModel, user_id: int) -> bool:
        return True

    async def create_jd_with_assignee(self, jd_data: JDModel) -> None:
        return None

    async def create_jd(self, jd_data: JDModel) -> JDModel:
        return JDModel(**jd_data.model_dump())

    async def get_jds(self, jd_ids: list[int]) -> list[JDResponse]:
        return [
            JDResponse(
                jd_id="123",
                jd_name="Software Engineer",
                jd_status="DRAFT",
                company_id=2,
                created_by=1,
                min_work_exp=1,
                max_work_exp=2,
                job_type="FULL_TIME",
                job_preference="ON_SITE",
                salary_currency="USD",
                min_salary=100000,
                max_salary=200000,
                application_form={
                    "name": "John Doe",
                    "email": "<EMAIL>",
                    "phone": "1234567890",
                    "resume": "resume.pdf",
                },
                jd_text="Software Engineer",
                designation="Software Engineer",
                primary_skills="Python, Django",
                secondary_skills="Flask, React",
                preferred_location="New York",
                company_name="Acme Inc.",
                notice_period="30 days",
                salary_duration="MONTHLY",
                hid="123",
            ),
        ]

    async def apply_filters_jd(self, user: AuthUserSchema, filters: JDFilters | None) -> str:
        return "filters applied"

    async def filter_by_assignees(
        self, user: AuthUserSchema, assignees: list[JDTaskStatusEnum]
    ) -> str:
        return "assignees filtered"

    async def update_jd(self, jd_id: int, jd_data: JDModel) -> JDModel:
        return JDModel(**jd_data.model_dump())

    async def update_jd_assignee(
        self, jd_id: int, jd_data: JDFullUpdateModel, company_id: int, user_id: int
    ) -> tuple[list[int], list[int]]:
        return ([1, 2], [3, 4])

    async def update_jd_embedding(self, jd_id: int, embedding: dict) -> bool:
        return True

    async def get_jds_gql(
        self,
        user: AuthUserSchema,
        offset: int,
        limit: int,
        jd_ids: list | None = None,
        fields: list[str] | None = None,
        filters: JDFilters | None = None,
        sort_by: JDSort | None = None,
    ) -> tuple[list[dict], int]:
        return ([], 0)

    async def get_jd_assignees(self, jd_ids: list[int]) -> list[dict]:
        return []

    async def search_jds(
        self, search_query: JDSearchQueryModel
    ) -> tuple[list[JDSearchResponseModel], int]:
        return ([], 0)

    async def delete_jd(self, jd_id: int) -> bool:
        return True

    async def crate_assign_users(
        self,
        assigned_by: int,
        jd_id: int,
        assignee_ids: list[str],
        company_id: int,
    ):
        return True

    async def get_application_form(self, jd_id: int) -> ApplyFormJDModel:
        return ApplyFormJDModel(
            jd_name="Software Engineer",
            jd_text="Design and develop software applications",
            designation="Software Engineer",
            min_work_exp=1,
            max_work_exp=5,
            job_type="FULL_TIME",
            job_preference="ON_SITE",
            primary_skills="Python, Django",
            secondary_skills="Flask, React",
            preferred_location="Bangalore",
            company_name="Acme Inc.",
            notice_period="30 days",
            salary_currency="USD",
            min_salary=100000,
            max_salary=200000,
            salary_duration="MONTHLY",
        )

    async def update_application_form(self, jd_id: int, jd_data: JDPartialUpdateModel) -> bool:
        return True

    async def update_career_form(self, jd_id: int, jd_data: JDPartialUpdateModel) -> bool:
        return True

    async def update_jd_status(self, jd_id: int, jd_data: JDPartialUpdateModel) -> bool:
        return True

    async def withdraw_from_jd(
        self, jd_id: int, jd_data: JDPartialUpdateModel, user_id: int, company_id: int
    ) -> bool:
        return True

    async def get_jd_embedding(self, jd_id: int, fields: list | None = None) -> dict:
        return {}

    async def get_jd_by_assignees(self, user_id: int) -> list[int]:
        return [1, 2]

    async def get_jds_for_dashboard(self, company_id: int) -> JDStatusModel:
        return JDStatusModel(active_jds=10, closed_jds=5, draft_jds=2, archived_jds=1)

    async def get_leaderboard(self, company_id: int) -> list[LeaderBoardModel]:
        return [
            LeaderBoardModel(user_name="Alice", closed_jds=5, assigned_jds=10),
            LeaderBoardModel(user_name="Bob", closed_jds=3, assigned_jds=8),
        ]
