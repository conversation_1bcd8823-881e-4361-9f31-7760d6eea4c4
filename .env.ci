ENVIRONMENT=test
LOG_LEVEL=20
SERVER_NAME=localhost
ORIGINS=http://localhost
ALLOWED_HOST=localhost

POSTGRES_HOST=localhost
POSTGRES_USER=test_user
POSTGRES_PASSWORD=test_password
POSTGRES_DB=test_db
POSTGRES_PORT=5432

REDIS_HOST=localhost
REDIS_PORT=6379

OPENAI_API_KEY=test-key
SENTRY_DSN=""

USER_SERVICE=""
RANKING_SERVICE=""
CANDIDATE_SERVICE=""
AI_SERVICE=""

FIREBASE_CONFIG=""
JWT_SECRET_KEY=supersecret
JWT_ALGORITHM=HS256
JWT_IDENTIFIER=test
