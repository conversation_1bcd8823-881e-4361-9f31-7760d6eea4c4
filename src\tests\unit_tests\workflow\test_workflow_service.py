# Creating a workflow with valid data returns a WorkflowModel instance
import pytest
from injector import Injector

from src.app.routers.scenarios.repositories.automation_repo import (
    AutomationRepo,
)
from src.app.routers.workflows.repoitories.workflow_repo import WorkflowRepo
from src.app.routers.workflows.schemas.workflow_schemas import CreateWorkflowModel
from src.app.routers.workflows.services.workflow_service import WorkflowService
from src.app.utils.schemas import BaseQueryParams
from src.tests.fake_data.fake_automation_repo import FakeAutomationRepo
from src.tests.fake_data.fake_workflow_repo import FakeWorkflowRepo


@pytest.fixture
def workflow_service():
    injector = Injector()
    injector.binder.bind(WorkflowRepo, to=FakeWorkflowRepo())
    injector.binder.bind(AutomationRepo, to=FakeAutomationRepo())
    return injector.get(WorkflowService)


@pytest.mark.asyncio
async def test_get_workflows(workflow_service: WorkflowService):
    retrieved_workflows = await workflow_service.get_workflows(
        query_params=BaseQueryParams(), company_id=1
    )
    assert len(retrieved_workflows) > 0


@pytest.mark.asyncio
async def test_get_workflow(workflow_service: WorkflowService):
    workflow_id = 1
    workflow = await workflow_service.get_workflow_by_id(workflow_id)
    assert workflow.id == workflow_id


@pytest.mark.asyncio
async def test_create_workflow(workflow_service: WorkflowService):
    data = CreateWorkflowModel(name="Sample Workflow", workflow={"stage": []})
    created_workflow = await workflow_service.create_workflow(data, created_by=1, company_id=1)
    assert created_workflow.name == "Sample Workflow"


@pytest.mark.asyncio
async def test_delete_workflow(workflow_service: WorkflowService):
    result = await workflow_service.delete_workflow(workflow_id=1, company_id=1)
    assert result == "success"


@pytest.mark.asyncio
async def test_update_workflow(workflow_service: WorkflowService):
    data = CreateWorkflowModel(name="jack", workflow={"stage": []})
    result = await workflow_service.update_workflow(workflow_id=1, company_id=1, data=data)
    assert result == "success"
