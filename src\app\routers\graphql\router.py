import or<PERSON><PERSON>
import strawberry
from fastapi import Depends, Request
from fastapi_injector import Injected
from strawberry.fastapi import Graph<PERSON><PERSON><PERSON><PERSON>
from strawberry.http import GraphQLHTTPResponse
from strawberry.schema.config import StrawberryConfig
from strawberry.types import ExecutionResult

from src.app.core.auth.authentication import get_current_user
from src.app.routers.graphql.context import CustomContext
from src.app.routers.graphql.jd.service.query_service import JD<PERSON><PERSON>y
from src.app.routers.jds.services.jd_service import JDService
from src.app.utils.response_helper import error_response, success_response
from src.app.utils.schemas.output_schemas import ErrorSchemas
from src.app.utils.schemas.user_schemas import AuthUserSchema


class MyGraphQLRouter(GraphQLRouter):
    """Custom GraphQLRouter class.
    Methods:
        process_result(self, request: Request, result: ExecutionResult):

    """

    async def process_result(
        self, request: Request, result: ExecutionResult
    ) -> GraphQLHTTPResponse:
        """
        Process the result of the GraphQL execution.

        If the result contains errors, a 400 error response is generated
        with the error details. Otherwise, a 200 success response is
        generated with the result data.

        Args:
            request (Request): The FastAPI request object.
            result (ExecutionResult): The result of the GraphQL execution.

        Returns:
            GraphQLHTTPResponse: The response object.
        """
        if result.errors:
            return orjson.loads(
                error_response(
                    request,
                    "VALIDATION_ERROR",
                    details=[
                        ErrorSchemas(
                            loc=err.path,
                            msg=err.formatted.get("message"),
                            type=err.formatted.get("type", ""),
                        )
                        for err in result.errors
                    ],
                ).body
            )
        metadata = getattr(request.state, "metadata", None)
        return orjson.loads(success_response(result.data, request, metadata=metadata).body)


async def custom_context_dependency(
    request: Request,
    jd_service: JDService = Injected(JDService),
    user: AuthUserSchema = Depends(get_current_user),
) -> CustomContext:
    """
    Generates a CustomContext object containing the current request,
    JDService and current user.

    Args:
        request (Request): The current request object.
        jd_service (JDService): The JDService instance.
        user (AuthUserSchema): The current user.

    Returns:
        CustomContext: The CustomContext object.
    """

    return CustomContext(request, jd_service, user)


async def get_context(
    custom_context: CustomContext = Depends(custom_context_dependency),
) -> CustomContext:
    """
    Get the CustomContext for the current request.

    Args:
        custom_context (CustomContext): The CustomContext object.

    Returns:
        CustomContext: The CustomContext object.
    """
    return custom_context


@strawberry.type
class Query(JDQuery):
    """Organizes the query context for GraphQL."""


schema = strawberry.Schema(Query, config=StrawberryConfig(auto_camel_case=False))

graphql_app = MyGraphQLRouter(schema, context_getter=get_context, allow_queries_via_get=True)
