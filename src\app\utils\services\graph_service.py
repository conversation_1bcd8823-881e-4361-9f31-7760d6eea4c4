from injector import inject, singleton

from src.app.utils import logger
from src.app.utils.repos.graph_repo import GraphRepository
from src.app.utils.schemas.graph_db_schema import JDSchema


@singleton
class RankingService:
    @inject
    def __init__(self, graph_repo: GraphRepository) -> None:
        self.graph_repo = graph_repo

    async def add_jd_to_graph(self, jd: JDSchema) -> bool:
        """
        Adds a job description to the graph database.

        Args:
            jd (JDSchema): The job description to add.

        Returns:
            bool: Whether the job description was successfully added.
        """
        if await self.graph_repo.add_jd_to_graph(jd):
            logger.info("Job description added to graph database")
            return True
        return False
