from typing import Literal

import or<PERSON><PERSON>
from injector import inject, singleton
from redis.asyncio import Redis

from src.app.routers.jds.schemas.jd_schemas import JDModel


@singleton
class JDCacheManagement:
    @inject
    def __init__(self, redis: Redis):
        self.redis = redis
        self.timeout = 60 * 60

    async def get_jd(self, jd_id: int) -> JDModel | None:
        data = await self.redis.get(str(jd_id))
        if not data:
            return None
        return JDModel(**orjson.loads(data))

    async def add_created_by(self, jd_id: int, created_by: int):
        await self.redis.sadd(f"{jd_id}:jd_created_by", str(created_by))  # type: ignore[misc]

    async def set_jd(self, jd_id: int, jd: JDModel, created_by: int | None = None):
        await self.redis.set(str(jd_id), orjson.dumps(jd.model_dump()), ex=self.timeout)
        if created_by:
            await self.add_created_by(jd_id, created_by)

    async def delete_jd(self, jd_id: int):
        await self.redis.delete(str(jd_id))

    async def set_assignee(self, jd_id: int, assignee_ids: list[int]):
        res = await self.redis.exists(f"{jd_id}:assignee_ids")
        if not res:
            await self.redis.delete(f"{jd_id}:assignee_ids")
        if not assignee_ids:
            return
        await self.redis.sadd(
            f"{jd_id}:assignee_ids",
            *[str(i) for i in assignee_ids],
        )  # type: ignore[misc]
        await self.redis.expire(f"{jd_id}:assignee_ids", self.timeout)

    async def get_assignee(
        self, jd_id: int, assignee_id: int
    ) -> Literal["Not Found", "Assigned", "Not Assigned"]:
        res = await self.redis.exists(f"{jd_id}:assignee_ids")
        if not res:
            return "Not Found"
        exists = await self.redis.sismember(f"{jd_id}:assignee_ids", str(assignee_id))  # type: ignore[misc]
        # check the created_by access
        if not exists:
            exists = await self.redis.sismember(f"{jd_id}:jd_created_by", str(assignee_id))  # type: ignore[misc]
        return "Assigned" if exists else "Not Assigned"

    async def delete_assignee(self, jd_id: int, assignee_id: list[int]):
        await self.redis.srem(f"{jd_id}:assignee_ids", *[str(i) for i in assignee_id])  # type: ignore[misc]
