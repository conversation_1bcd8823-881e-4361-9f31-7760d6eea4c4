"""
Simple Locust test for Workflow Creation API.
Only tests POST /v1/workflows endpoint.
"""

from datetime import datetime
import random
from locust import HttpUser, task, between


class WorkflowTest(HttpUser):
    """Simple load test for workflow creation endpoint only."""

    wait_time = between(1, 3)

    def on_start(self):
        """Setup authentication and counter"""
        self.client.headers.update({
            "Authorization": "Bearer test_token_for_load_testing",
            "Content-Type": "application/json"
        })
        self.workflow_counter = 0

    def generate_workflow_payload(self):
        """Generate workflow data"""
        self.workflow_counter += 1
        timestamp = datetime.now().strftime("%H%M%S%f")[:9]
        
        return {
            "name": f"Test Workflow {self.workflow_counter} - {timestamp}",
            "workflow": {
                "stage": [
                    {
                        "automations": [101, 102, 103],
                        "name": "Initial Screening"
                    },
                    {
                        "automations": [201, 202],
                        "name": "Technical Interview"
                    },
                    {
                        "automations": [301],
                        "name": "HR Round"
                    }
                ],
                "version": 1
            }
        }

    @task
    def create_workflow(self):
        """Create workflow - only this endpoint"""
        payload = self.generate_workflow_payload()
        
        self.client.post("/v1/workflows", json=payload)