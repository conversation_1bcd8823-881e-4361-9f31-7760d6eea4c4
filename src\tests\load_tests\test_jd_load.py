import json
import random
import time
import uuid
from datetime import datetime, timed<PERSON>ta

from locust import Http<PERSON><PERSON>, between, task


class JDUser(HttpUser):
    wait_time = between(1, 3)  # Wait 1-3 seconds between requests

    def on_start(self):
        """Setup authentication and counter"""
        self.client.headers.update({
            "Authorization": "Bearer test_token_for_load_testing",
            "Content-Type": "application/json"
        })
        self.workflow_counter = 0

    @task()
    def create_jd(self):
        """Test creating a new JD"""
        
        # Generate unique identifiers to avoid duplicate key errors
        timestamp = int(time.time() * 1000)  # Millisecond timestamp
        unique_id = str(uuid.uuid4())[:8]    # Short unique ID
        
        # Sample JD data with unique values
        jd_data = {
            "jd_name": f"Senior Backend Engineer - Java {timestamp}_{unique_id}",
            "jd_status": random.choice(["DRAFT", "ACTIVE", "CLOSED"]),
            "assignee_ids": [str(random.randint(100, 200))],
            "jd_text": "We are seeking an experienced Backend Engineer with strong Python and Django skills to join our growing team. Experience with cloud infrastructure and microservices is a plus.",
            "hiring_priority": random.choice(["HIGH", "MEDIUM", "LOW"]),
            "designation": "Senior Backend Engineer",
            "min_work_exp": random.randint(3, 5),
            "max_work_exp": random.randint(6, 10),
            "job_type": random.choice(["FULL_TIME", "CONTRACT", "INTERNSHIP"]),
            "job_preference": random.choice(["REMOTE", "ON_SITE", "HYBRID"]),
            "primary_skills": "Python, Django, REST APIs",
            "secondary_skills": "AWS, PostgreSQL, Docker",
            "preferred_location": "Remote, India",
            "no_of_positions": random.randint(1, 5),
            "company_name": f"TechNova Solutions {unique_id}",
            "client_name": f"FinTrust Bank {unique_id}",
            "company_url": "https://www.technova.io",
            "department": "HR",
            "notice_period": "15 days maximum",
            "salary_currency": "INR",
            "min_salary": random.randint(1200000, 1500000),
            "max_salary": random.randint(1800000, 2500000),
            "salary_duration": "YEARLY",
            "hourly_rate": str(random.randint(800, 1200)),
            "confirmed_start_date": "2025-08-01T00:00:00.000Z",
            "bill_to_client": "FinTrust Bank - Monthly Retainer",
            "contract_acceptance": True,
            "recommendation_skills": "Python, Django, FastAPI",
            "recommendation_keywords": "Backend, Python, Django, Fintech",
            "recommendation_companies": "Paytm, Razorpay, CRED",
            "recommendation_industries": "Fintech, SaaS",
            "recommendation_colleges": "IIT, NIT, BITS Pilani",
            "targets_shortlisted": random.randint(8, 15),
            "targets_shortlisted_date": "2025-07-30T00:00:00.000Z",
            "targets_hiring_date": "2025-08-10T00:00:00.000Z",
            "tax_term": "C2C",
            "vendor_email": f"vendor_{unique_id}@talenthire.com",
            "vendor_phone": "+91-**********",
            "vendor_link": "https://talenthire.com/vendor-profile/fintrust"
        }
        
        # Make the API call to create JD
        with self.client.post(
            "/v1/jds", 
            json=jd_data,
            catch_response=True,
            name="Create JD"
        ) as response:
            
            if response.status_code == 200 or response.status_code == 201:
                response.success()
                print(f"✅ JD created successfully: {response.status_code}")
                
                # Extract JD ID from response for embedding call
                try:
                    response_data = response.json()
                    if 'data' in response_data and 'id' in response_data['data']:
                        jd_id = response_data['data']['id']
                        # Call the embedding endpoint with the created JD ID
                        self.get_jd_embedding(jd_id)
                except Exception as e:
                    print(f"Could not extract JD ID: {e}")
                    
            else:
                response.failure(f"Failed to create JD: {response.status_code} - {response.text}")
                print(f"❌ Failed: {response.status_code} - {response.text}")

    def get_jd_embedding(self, jd_id):
        """Get JD embedding for the created JD"""
        with self.client.get(
            f"/v1/jds/{jd_id}/embedding",
            catch_response=True,
            name="Get JD Embedding"
        ) as response:
            
            if response.status_code == 200:
                response.success()
                print(f"✅ JD embedding retrieved successfully for ID: {jd_id}")
            else:
                response.failure(f"Failed to get JD embedding: {response.status_code} - {response.text}")
                print(f"❌ Embedding failed for ID {jd_id}: {response.status_code}")

    def generate_workflow_payload(self):
        """Generate workflow data"""
        self.workflow_counter += 1
        timestamp = datetime.now().strftime("%H%M%S%f")[:9]
        
        return {
            "name": f"Test Workflow {self.workflow_counter} - {timestamp}",
            "workflow": {
                "stage": [
                    {
                        "automations": [101, 102, 103],
                        "name": "Initial Screening"
                    },
                    {
                        "automations": [201, 202],
                        "name": "Technical Interview"
                    },
                    {
                        "automations": [301],
                        "name": "HR Round"
                    }
                ],
                "version": 1
            }
        }

    @task()
    def create_workflow(self):
        """Create workflow"""
        payload = self.generate_workflow_payload()
        
        with self.client.post(
            "/v1/workflows", 
            json=payload,
            catch_response=True,
            name="Create Workflow"
        ) as response:
            
            if response.status_code == 200 or response.status_code == 201:
                response.success()
                print(f"✅ Workflow created successfully: {response.status_code}")
            else:
                response.failure(f"Failed to create workflow: {response.status_code} - {response.text}")
                print(f"❌ Workflow failed: {response.status_code} - {response.text}")

    @task()  # Medium weight
    def create_scenario(self):
        """Test creating a new scenario"""
        
        # Generate unique identifiers to avoid duplicate scenarios
        timestamp = int(time.time() * 1000)  # Millisecond timestamp
        unique_id = str(uuid.uuid4())[:8]    # Short unique ID
        
        # List of different categories to test variety
        categories = [
            "Data Science",
            "Software Engineering", 
            "DevOps",
            "Machine Learning",
            "Backend Development",
            "Frontend Development",
            "Full Stack Development",
            "Cloud Engineering",
            "Product Management",
            "QA Engineering"
        ]
        
        # List of different descriptions for variety
        descriptions = [
            "Responsible for building ML models and handling data pipelines using Python and cloud platforms.",
            "Developing scalable web applications using modern frameworks and best practices.",
            "Managing cloud infrastructure and implementing CI/CD pipelines for efficient deployment.",
            "Creating responsive user interfaces and optimizing frontend performance.",
            "Leading product development and coordinating cross-functional teams.",
            "Ensuring quality through automated testing and manual verification processes.",
            "Building and maintaining backend services with focus on performance and reliability.",
            "Implementing machine learning algorithms and data processing workflows.",
            "Managing containerized applications and orchestrating microservices architecture.",
            "Full-stack development with expertise in both frontend and backend technologies."
        ]
        
        # Create scenario data with randomization
        scenario_data = {
            "category": f"{random.choice(categories)} {unique_id}",
            "description": f"{random.choice(descriptions)} [Test ID: {timestamp}]"
        }
        
        # Make the API call
        with self.client.post(
            "/v1/scenarios", 
            json=scenario_data,
            catch_response=True,
            name="Create Scenario"
        ) as response:
            
            if response.status_code == 200 or response.status_code == 201:
                response.success()
                print(f"✅ Scenario created successfully: {response.status_code}")
                
                # Optional: Log the created scenario details
                try:
                    response_data = response.json()
                    if 'data' in response_data and 'id' in response_data['data']:
                        scenario_id = response_data['data']['id']
                        print(f"📝 Created scenario ID: {scenario_id}")
                except Exception as e:
                    print(f"Could not extract scenario ID: {e}")
                    
            else:
                response.failure(f"Failed to create scenario: {response.status_code} - {response.text}")
                print(f"❌ Scenario failed: {response.status_code} - {response.text}")

    @task()
    def create_automation(self):
        """Test POST /v1/scenarios/{scenario_id}/automations"""
        
        # Just use random scenario ID for testing - doesn't need to be real
        scenario_id = random.randint(1, 100)
        
        # Generate automation data - using your correct payload format
        current_time = datetime.utcnow()
        time_str = current_time.strftime("%H:%M:%S.%f")[:-3]  # Format: HH:MM:SS.mmm
        
        automation_data = {
            "description": "Welcome email for new user registration",
            "template_id": 101,
            "channel_type": "EMAIL",
            "email_type": "PERSONAL",
            "schedule_type": "IMMEDIATELY",
            "schedule_time": time_str  # Format: "05:17:51.991"
        }
        
        # Make API call
        with self.client.post(
            f"/v1/scenarios/{scenario_id}/automations",
            json=automation_data,
            catch_response=True,
            name="Create Automation"
        ) as response:
            
            if response.status_code == 200 or response.status_code == 201:
                response.success()
                print(f"✅ Automation success for scenario {scenario_id}")
            elif response.status_code == 404 or response.status_code == 400:
                # Expected for testing - scenario doesn't exist
                response.success()  # Mark as success since this is expected for testing
                print(f"📝 Testing automation for scenario {scenario_id} (expected)")
            else:
                response.failure(f"Failed: {response.status_code}")
                print(f"❌ Automation failed for scenario {scenario_id}: {response.status_code}")

# Run with: locust -f multi_api_test.py --host=http://your-api-host.com