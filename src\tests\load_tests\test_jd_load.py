# """
# Simple Locust test for Workflow Creation API.
# Only tests POST /v1/workflows endpoint.
# """

# from datetime import datetime
# import random
# from locust import HttpUser, task, between


# class WorkflowTest(HttpUser):
#     """Simple load test for workflow creation endpoint only."""

#     wait_time = between(1, 3)

#     def on_start(self):
#         """Setup authentication and counter"""
#         self.client.headers.update({
#             "Authorization": "Bearer test_token_for_load_testing",
#             "Content-Type": "application/json"
#         })
#         self.workflow_counter = 0

#     def generate_workflow_payload(self):
#         """Generate workflow data"""
#         self.workflow_counter += 1
#         timestamp = datetime.now().strftime("%H%M%S%f")[:9]
        
#         return {
#             "name": f"Test Workflow {self.workflow_counter} - {timestamp}",
#             "workflow": {
#                 "stage": [
#                     {
#                         "automations": [101, 102, 103],
#                         "name": "Initial Screening"
#                     },
#                     {
#                         "automations": [201, 202],
#                         "name": "Technical Interview"
#                     },
#                     {
#                         "automations": [301],
#                         "name": "HR Round"
#                     }
#                 ],
#                 "version": 1
#             }
#         }

#     @task
#     def create_workflow(self):
#         """Create workflow - only this endpoint"""
#         payload = self.generate_workflow_payload()
        
#         self.client.post("/v1/workflows", json=payload)


from locust import HttpUser, task, between
import json
import random
import time
import uuid

class JDUser(HttpUser):
    wait_time = between(1, 3)  # Wait 1-3 seconds between requests
    
    def on_start(self):
        """Setup method - runs once when user starts"""
        # Add your authentication here if needed
        # Example:
        # self.client.post("/auth/login", json={"username": "test", "password": "test"})
        
        # Set headers if needed
        self.client.headers.update({
            "Content-Type": "application/json",
            # "Authorization": "Bearer your_token_here"  # Add if auth required
        })
    
    @task
    def create_jd(self):
        """Test creating a new JD"""
        
        # Generate unique identifiers to avoid duplicate key errors
        timestamp = int(time.time() * 1000)  # Millisecond timestamp
        unique_id = str(uuid.uuid4())[:8]    # Short unique ID
        
        # Sample JD data with unique values
        jd_data = {
            "jd_name": f"Senior Backend Engineer - Java {timestamp}_{unique_id}",
            "jd_status": random.choice(["DRAFT", "ACTIVE", "CLOSED"]),
            "assignee_ids": [str(random.randint(100, 200))],
            "jd_text": "We are seeking an experienced Backend Engineer with strong Python and Django skills to join our growing team. Experience with cloud infrastructure and microservices is a plus.",
            "hiring_priority": random.choice(["HIGH", "MEDIUM", "LOW"]),
            "designation": "Senior Backend Engineer",
            "min_work_exp": random.randint(3, 5),
            "max_work_exp": random.randint(6, 10),
            "job_type": random.choice(["FULL_TIME", "CONTRACT", "INTERNSHIP"]),
            "job_preference": random.choice(["REMOTE", "ON_SITE", "HYBRID"]),
            "primary_skills": "Python, Django, REST APIs",
            "secondary_skills": "AWS, PostgreSQL, Docker",
            "preferred_location": "Remote, India",
            "no_of_positions": random.randint(1, 5),
            "company_name": f"TechNova Solutions {unique_id}",
            "client_name": f"FinTrust Bank {unique_id}",
            "company_url": "https://www.technova.io",
            "department": "HR",
            "notice_period": "15 days maximum",
            "salary_currency": "INR",
            "min_salary": random.randint(1200000, 1500000),
            "max_salary": random.randint(1800000, 2500000),
            "salary_duration": "YEARLY",
            "hourly_rate": str(random.randint(800, 1200)),
            "confirmed_start_date": "2025-08-01T00:00:00.000Z",
            "bill_to_client": "FinTrust Bank - Monthly Retainer",
            "contract_acceptance": True,
            "recommendation_skills": "Python, Django, FastAPI",
            "recommendation_keywords": "Backend, Python, Django, Fintech",
            "recommendation_companies": "Paytm, Razorpay, CRED",
            "recommendation_industries": "Fintech, SaaS",
            "recommendation_colleges": "IIT, NIT, BITS Pilani",
            "targets_shortlisted": random.randint(8, 15),
            "targets_shortlisted_date": "2025-07-30T00:00:00.000Z",
            "targets_hiring_date": "2025-08-10T00:00:00.000Z",
            "tax_term": "C2C",
            "vendor_email": f"vendor_{unique_id}@talenthire.com",
            "vendor_phone": "+91-**********",
            "vendor_link": "https://talenthire.com/vendor-profile/fintrust"
        }
        
        # Make the API call
        with self.client.post(
            "/v1/jds", 
            json=jd_data,
            catch_response=True,
            name="Create JD"  # This name will appear in Locust UI
        ) as response:
            
            # Check if request was successful
            if response.status_code == 200 or response.status_code == 201:
                response.success()
                print(f"✅ JD created successfully: {response.status_code}")
            else:
                response.failure(f"Failed to create JD: {response.status_code} - {response.text}")
                print(f"❌ Failed: {response.status_code} - {response.text}")

