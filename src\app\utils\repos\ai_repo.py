import httpx
from injector import inject, singleton

from src.app.core import logger
from src.app.core.auth.server_to_server_auth import ServerToServerAuth
from src.app.core.config.settings import get_settings
from src.app.utils.schemas.ai_schemas import EmbeddingListResponseSchema, EmbeddingRequestSchema


@singleton
class AIRepo:
    @inject
    def __init__(self, auth_service: ServerToServerAuth):
        """
        Initializes the AIRepo with base service URL and timeout.
        """
        self.base_url = get_settings().AI_SERVICE
        self.auth_service = auth_service
        self.authentication = auth_service.generate_token()
        self.timeout = 300.0

    async def fetch_embedding_from_api(self, text: str) -> dict | None:
        """
        Fetches an embedding from the AI service using a single text input.

        Args:
            text (str): The input text to embed.

        Returns:
            Optional[dict]: Embedding information (embedding, model, dimension), or None on failure.
        """
        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    f"{self.base_url}/v2/embeddings/generate",
                    json=EmbeddingRequestSchema(contents=[text]).model_dump(),
                    timeout=self.timeout,
                    headers={
                        "Content-Type": "application/json",
                        "Authorization": f"Bearer {self.authentication}",
                    },
                )
                response.raise_for_status()
                result = response.json()
                logger.info(f"embedding result -> {result}")
                parsed = EmbeddingListResponseSchema(**result["data"])

                if not parsed.embeddings:
                    return None

                emb = parsed.embeddings[0]
                return {
                    "embedding": emb.embedding,
                    "status": "generated",
                    "model": getattr(parsed, "model", "text-embedding-3-small"),
                    "dimension": len(emb.embedding),
                }

            except (httpx.HTTPStatusError, httpx.RequestError):
                return None
