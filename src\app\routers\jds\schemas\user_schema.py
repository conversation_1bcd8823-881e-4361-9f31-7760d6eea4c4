from pydantic import BaseModel, field_validator


class AssigneeSchema(BaseModel):
    id: int
    user_id: str
    user_name: str
    designation: str
    user_type_str: str
    email: str | None
    image_url: str | None
    created_at: str
    modified_at: str

    @field_validator("user_id", mode="before")
    def convert_user_id_str(cls, user_id) -> str:
        if isinstance(user_id, int):
            return str(user_id)
        return user_id

    class Config:
        extra = "allow"
