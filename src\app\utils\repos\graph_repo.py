import httpx
from injector import inject, singleton

from src.app.core.auth.server_to_server_auth import ServerToServerAuth
from src.app.core.config.settings import get_settings
from src.app.utils.schemas.graph_db_schema import JDSchema


@singleton
class GraphRepository:
    @inject
    def __init__(self, auth_service: ServerToServerAuth) -> None:
        self.service = get_settings().RANKING_SERVICE
        self.authentication = auth_service.generate_token()

    async def add_jd_to_graph(self, jd: JDSchema) -> bool:
        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    f"{self.service}/v1/jds",
                    json=jd.model_dump(),
                    headers={
                        "Content-Type": "application/json",
                        "Authorization": f"Bearer {self.authentication}",
                    },
                    timeout=5.0,
                )
                response.raise_for_status()
                return response.status_code == "200"
            except (httpx.HTTPStatusError, httpx.RequestError):
                return False
