from tortoise import fields
from tortoise.indexes import Index

from src.app.db.tables.inheritance_table import CustomManager, TimestampMixin
from src.app.db.tables.jd_constants import JDTableConst
from src.app.db.tables.jd_enums import (
    DepartmentType,
    JDStatusType,
    JobType,
    PresenceType,
    PriorityType,
    SalaryDurationType,
)


class JDTable(TimestampMixin):
    jd_id = fields.BigIntField(null=False, pk=True, generated=False)
    hid = fields.CharField(max_length=255, null=False, unique=True)
    jd_name = fields.CharField(max_length=255, null=False)
    jd_text = fields.TextField(
        null=False, description="The full text(description) content of the JD"
    )
    company_id = fields.IntField(null=False, description="The ID of the company posting the JD.")
    hiring_priority = fields.IntEnumField(PriorityType, null=False)
    designation = fields.CharField(max_length=255, null=False)
    min_work_exp = fields.IntField(null=False)
    max_work_exp = fields.IntField(null=True)
    job_type = fields.IntEnumField(JobType, description="Job type", null=False)
    job_preference = fields.IntEnumField(PresenceType, description="Job presence", null=False)
    primary_skills = fields.TextField(null=False)
    secondary_skills = fields.TextField(null=True)
    preferred_location = fields.TextField(null=False)
    no_of_positions = fields.SmallIntField(null=False)
    company_name = fields.CharField(null=False, max_length=255)
    company_url = fields.CharField(blank=True, null=True, max_length=1000)
    department = fields.IntEnumField(DepartmentType, null=True)
    # it will be in days
    notice_period_in_days = fields.SmallIntField(null=True)
    notice_period = fields.CharField(blank=True, null=False, max_length=255)
    # salary
    salary_currency = fields.CharField(null=True, max_length=3)
    min_salary = fields.FloatField(null=True)
    max_salary = fields.FloatField(null=True)
    salary_duration = fields.IntEnumField(SalaryDurationType, description="Salary type", null=True)
    #  if contract type
    hourly_rate = fields.CharField(
        null=True, description="Hourly rate for contract type", max_length=100
    )
    confirmed_start_date = fields.DateField(
        null=True, description="Confirmed start date for contract type"
    )
    bill_to_client = fields.CharField(
        null=True, max_length=100, description="Bill to client for contract type"
    )
    contract_acceptance = fields.BooleanField(
        null=True, description="Contract acceptance for contract type"
    )
    tax_term = fields.CharField(null=True, max_length=100, description="Tax term for contract type")
    # recommendation
    recommendation_skills = fields.CharField(null=True, max_length=1000)
    recommendation_keywords = fields.CharField(null=True, max_length=1000)
    recommendation_companies = fields.CharField(null=True, max_length=1000)
    recommendation_industries = fields.CharField(null=True, max_length=1000)
    recommendation_colleges = fields.CharField(null=True, max_length=1000)
    # target
    targets_shortlisted = fields.SmallIntField(null=True)
    targets_shortlisted_date = fields.DateField(null=True)
    targets_hiring_date = fields.DateField(null=True)
    # vendor
    vendor_email = fields.CharField(null=True, max_length=255)
    vendor_phone = fields.CharField(null=True, max_length=255)
    vendor_link = fields.CharField(null=True, max_length=1000)

    # this is application form
    application_form = fields.JSONField(default=dict, null=True)
    career_page = fields.JSONField(default=dict, null=True)
    created_by = fields.IntField(
        description="User ID as FK",
        db_index=True,
    )
    jd_embedding = fields.JSONField(default=dict)
    client_name = fields.CharField(null=True, max_length=255)
    jd_status = fields.IntEnumField(JDStatusType, description="JD Status")

    class Meta:
        schema = "jd"
        table = "t_jd"
        manager = CustomManager()
        indexes = [Index(fields=("jd_id", "company_id"))]

    def __str__(self):
        return self.jd_name

    def to_dict(self) -> dict:
        return {field_name: getattr(self, field_name) for field_name in self._meta.fields}


class JDAssignTable(TimestampMixin):
    """Represents the assignment of a job description to a user.

    Attributes:
        id (IntField): Primary key for the assignment.
        jd (ForeignKeyField): Reference to the associated JD.
        assignee_id (BigIntField): User ID of the assignee.
        assignor_id (BigIntField): User ID of the assignor.
        is_unassigned (BooleanField): Indicates if the JD is currently
                                      unassigned.
    Methods:
        get_related_data(user_id): Fetches related user data from API.
        assignee (property): Asynchronously gets assignee's data.
        assignor (property): Asynchronously gets assignor's data.
        __str__(): Returns string representation of the assignment.
    """

    id = fields.IntField(pk=True)
    company_id = fields.IntField(null=False)
    jd = fields.ForeignKeyField(JDTableConst.JD_TABLE, related_name="jd_assign")
    assignee_id = fields.IntField(
        description="User ID as FK",
        db_index=True,
    )
    assignor_id = fields.IntField(
        description="User ID as FK",
        db_index=True,
    )
    comment = fields.TextField(null=True)
    is_unassigned = fields.BooleanField(default=False)
    is_withdrawn = fields.BooleanField(default=False)

    class Meta:
        schema = "jd"
        table = "t_jd_assign"
        manager = CustomManager()
        unique_together = (("jd_id", "assignee_id"),)

    def __str__(self):
        return f"""JDAssign(assignor_id={self.assignor_id}
                    to assignee_id={self.assignee_id}"""
