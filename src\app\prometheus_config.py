from prometheus_fastapi_instrumentator import Instrumentator
from prometheus_fastapi_instrumentator.metrics import (
    default,
    latency,
    request_size,
    response_size,
)


instrumentator = Instrumentator(
    should_group_status_codes=True,
    should_ignore_untemplated=True,
    should_respect_env_var=True,
    excluded_handlers=["/metrics"],  # don't instrument the metrics endpoint
    env_var_name="ENABLE_METRICS",
    should_group_untemplated=True,
)

# Register common metrics
instrumentator.add(default())
instrumentator.add(latency())
instrumentator.add(request_size())
instrumentator.add(response_size())
