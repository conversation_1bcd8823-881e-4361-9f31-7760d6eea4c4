import asyncio

from injector import inject, singleton
from redis.asyncio import Redis

from src.app.routers.jds.repositories.jd_workflow_repo import JDWorkflowRepo
from src.app.routers.jds.schemas.jd_workflow_schemas import CreateJDWorkflowModel
from src.app.routers.scenarios.repositories.automation_repo import AutomationRepo
from src.app.routers.workflows.schemas.workflow_schemas import WorkflowModel
from src.app.utils.services.user_service import UserService


@singleton
class JDWorkflowService:
    """Handles workflow operations related to Job Descriptions.

    Attributes:
        jd_repo (JDWorkflowRepo): Repository for JDWorkflow data.
        automation_repo (AutomationRepo): Repository for Automation data.
    """

    @inject
    def __init__(
        self,
        jd_repo: JDWorkflowRepo,
        automation_repo: AutomationRepo,
        user_service: UserService,
        redis: Redis,
    ):
        """Initializes JDWorkflowService with required repositories.

        Args:
            jd_repo (JDWorkflowRepo): An instance of JDWorkflowRepo.
            automation_repo (AutomationRepo): An instance of AutomationRepo.
            user_service (UserService): An instance of UserService.
        """
        self.jd_repo = jd_repo
        self.automation_repo = automation_repo
        self.user_service = user_service
        self.redis = redis

    async def get_workflow_by_id(self, jd_id: int) -> WorkflowModel:
        """Retrieves workflow details by JD ID.

        Args:
            jd_id (int): The ID of the JD.

        Returns:
            WorkflowModel: The WorkflowModel containing workflow details, or None if not found.
        """
        data = await self.jd_repo.get_workflow_by_id(jd_id)
        if not data.workflow:
            return data

        # get automation
        ids = []
        for i in data.workflow["stage"]:
            ids.extend(i["automations"])
        
        unique_ids = list(set(ids))

        assignee, automation = await asyncio.gather(
            self.user_service.get_user_by_ids([data.last_updated_by_id]),  # type: ignore
            self.automation_repo.get_automations(unique_ids),
        )


        data.workflow["assignee"] = assignee[data.last_updated_by_id]  # type: ignore[index]
        data.workflow["automations"] = automation

        return data

    async def update_or_create(
        self, jd_id: int, data: CreateJDWorkflowModel, user_id: int
    ) -> WorkflowModel:
        """Updates or creates a workflow for a given JD.

        Args:
            user_id (int): The ID of the user who created or updated the workflow.
            jd_id (int): The ID of the JD.
            data (CreateJDWorkflowModel): The workflow data to update or create.

        Returns:
            WorkflowModel: The updated or created WorkflowModel.
        """
        await self.redis.delete(f"jd_stage_{jd_id}")
        await self.redis.delete(f"jd_stage_automation_{jd_id}")
        return await self.jd_repo.update_or_create(jd_id, data, user_id)


