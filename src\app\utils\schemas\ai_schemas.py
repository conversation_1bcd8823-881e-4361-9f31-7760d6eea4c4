from pydantic import BaseModel, Field


class EmbeddingRequestSchema(BaseModel):
    contents: list[str] = Field(
        ...,
        description="List of Text content to generate embedding for",
        min_length=1,
    )


class EmbeddingResponseSchema(BaseModel):
    embedding: list[float] = Field(..., description="Generated embedding vector")
    model_name: str = Field(..., description="Name of the model used for embedding")
    size: int = Field(..., description="Size of the embedding vector")


class EmbeddingListResponseSchema(BaseModel):
    embeddings: list[EmbeddingResponseSchema] = Field(
        ...,
        description="List of generated embeddings for the provided content",
    )
