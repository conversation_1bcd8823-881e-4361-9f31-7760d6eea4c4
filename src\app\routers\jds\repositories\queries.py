class Queries:
    def build_jd_search_query(self, is_skills: bool, is_designation: bool, is_exp: bool) -> str:
        base_query = """
            SELECT DISTINCT
                jd.designation,
                jd.modified_at,
                jd.hid,
                jd.company_name,
                jd.preferred_location as location,
                jd.notice_period,
                CONCAT_WS(', ', jd.primary_skills, jd.secondary_skills) as skills,
                ROUND((COALESCE(jd.max_work_exp, 0) + jd.min_work_exp) / 2, 1) as years_of_exp
            FROM jd.t_jd jd
        """
        conditions = []
        current_placeholder = 1

        if is_skills:
            # Add skills condition with proper grouping
            base_query += """
                , unnest(string_to_array(LOWER(primary_skills), ',')) as primary_skill
            """
            skill_condition = """(
                TRIM(primary_skill) = ANY($1)
                OR EXISTS (
                    SELECT 1
                    FROM unnest(string_to_array(LOWER(secondary_skills), ',')) as secondary_skill
                    WHERE TRIM(secondary_skill) = ANY($1)
                )
            )"""
            conditions.append(skill_condition)
            current_placeholder += 1

        if is_designation:
            # Add designation condition
            conditions.append(f"designation ILIKE ${current_placeholder}")
            current_placeholder += 1

        if is_exp:
            # Add location condition
            conditions.append(f"""
            (
                (min_work_exp <= ${current_placeholder}
                AND max_work_exp >= ${current_placeholder}) OR
                (min_work_exp IS NULL AND max_work_exp >= ${current_placeholder}) OR
                (max_work_exp IS NULL AND min_work_exp <= ${current_placeholder})
            )
        """)
            current_placeholder += 1

        # Combine all conditions with proper logic
        if conditions:
            base_query += " WHERE " + " OR ".join(conditions)
            base_query += (
                f" AND status = true"
                f" AND company_id = ${current_placeholder}"
                f" AND jd_status = ${current_placeholder + 1}"
            )
        else:
            base_query += f" WHERE status = true AND jd_status=${current_placeholder}"

        # Add ordering
        base_query += " ORDER BY jd.modified_at DESC"

        return base_query

    def get_jds_count(self) -> str:
        return """
            SELECT
                jd.jd_status,
                COUNT(DISTINCT jd.jd_id) AS count
            FROM jd.t_jd jd
            WHERE jd.company_id = $1
            GROUP BY jd.jd_status
        """

    def build_get_leaderboard(self) -> str:
        return """
            SELECT
                tja.assignee_id,
                tu.user_name,
                SUM(CASE WHEN jd.jd_status = 3 THEN 1 ELSE 0 END) AS closed_jds,
                COUNT(*) AS assigned_jds
            FROM
                jd.t_jd jd
            JOIN
                jd.t_jd_assign tja ON tja.jd_id = jd.jd_id
            LEFT JOIN
                user_schema.t_user tu ON tu.id = tja.assignee_id
            WHERE
                tja.is_unassigned IS NOT TRUE
                AND jd.company_id = $1
                AND jd.modified_at::DATE BETWEEN
                    DATE_TRUNC('day', CURRENT_DATE - INTERVAL '60 days')::DATE
                    AND DATE_TRUNC('day', CURRENT_DATE)::DATE
            GROUP BY
                tja.assignee_id,
                tu.user_name
            ORDER BY
                closed_jds DESC;
        """
