from tortoise import fields

from src.app.db.tables.inheritance_table import CustomManager, TimestampMixin
from src.app.db.tables.jd_constants import JDTableConst
from src.app.db.tables.workflow_enums import (
    AutomationChannelType,
    AutomationEmailType,
    AutomationScheduleType,
)


class ScenarioTable(TimestampMixin):
    id = fields.IntField(pk=True)
    company_id = fields.IntField(null=True)
    is_generated = fields.BooleanField(null=False, default=False)
    category = fields.CharField(max_length=255, null=False)
    description = fields.TextField(null=False)
    created_by = fields.IntField(null=True)

    class Meta:
        schema = "jd"
        table = "t_scenario"
        manager = CustomManager()

    def __str__(self):
        return f"""ScenarioTable(id={self.id}, company_id={self.company_id},
        category={self.category})"""


class AutomationTable(TimestampMixin):
    id = fields.IntField(pk=True)
    company_id = fields.IntField(null=True)
    scenario = fields.ForeignKeyField(
        JDTableConst.JD_SCENARIO_TABLE, related_name="jd_automation", null=False
    )
    description = fields.CharField(null=True, max_length=255)
    template_id = fields.IntField(null=False)
    channel_type = fields.IntEnumField(AutomationChannelType, description="channel_type")
    email_type = fields.IntEnumField(AutomationEmailType, description="email_type", null=True)
    schedule_type = fields.IntEnumField(AutomationScheduleType, description="schedule_type")
    schedule_time = fields.TimeField(null=True)
    created_by = fields.IntField(null=True)

    class Meta:
        schema = "jd"
        table = "t_automation"
        manager = CustomManager()

    def __str__(self):
        return f"""AutomationTable(id={self.id}, company_id={self.company_id},
        scenario_id={self.scenario})"""


class WorkflowTable(TimestampMixin):
    id = fields.IntField(pk=True)
    company_id = fields.IntField(null=True)
    is_generated = fields.BooleanField(null=False, default=False)
    name = fields.CharField(null=False, max_length=255, unique=True)
    workflow = fields.JSONField(null=False)
    created_by = fields.IntField(null=True)

    class Meta:
        schema = "jd"
        table = "t_workflow"
        manager = CustomManager()

    def __str__(self):
        return f"""WorkflowTable(id={self.id}, company_id={self.company_id},
        name={self.name})"""


class WorkflowJDTable(TimestampMixin):
    id = fields.IntField(pk=True)
    jd = fields.ForeignKeyField(JDTableConst.JD_TABLE, related_name="workflow_jd", null=False)
    workflow = fields.ForeignKeyField(
        JDTableConst.JD_WORKFLOW_TABLE, related_name="workflow_base", null=False
    )

    local_workflow = fields.JSONField(null=True)

    last_updated_by = fields.IntField(null=False)

    class Meta:
        schema = "jd"
        table = "t_workflow_jd"
        manager = CustomManager()
        unique_together = (("jd", "workflow"),)

    def __str__(self):
        return f"""WorkflowUserTable(id={self.id}, jd_id={self.jd},
        workflow_id={self.workflow}"""
