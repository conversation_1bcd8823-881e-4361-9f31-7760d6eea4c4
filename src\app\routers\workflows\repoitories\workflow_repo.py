from injector import singleton
from tortoise.expressions import Q

from src.app.core.exceptions.resources_exceptions import InvalidOperationError
from src.app.db.tables.workflow_tables import WorkflowTable
from src.app.routers.workflows.repoitories.abstract_workflow_repo import AbstractWorkflowRepo
from src.app.routers.workflows.schemas.workflow_schemas import CreateWorkflowModel, WorkflowModel
from src.app.utils.schemas import BaseQueryParams, SortBy


@singleton
class WorkflowRepo(AbstractWorkflowRepo):
    """Repository for managing workflows."""

    async def create_workflow(
        self,
        data: CreateWorkflowModel,
        created_by: int,
        company_id: int,
    ) -> WorkflowModel:
        """Creates a new workflow.

        Args:
            data (CreateWorkflowModel): The data for the new workflow.
            created_by (int): The ID of the user creating the workflow.
            company_id (int): The ID of the company the workflow belongs to.

        Returns:
            WorkflowModel: The created workflow.
        """
        # TODO: Verify automations are correct or now
        table = WorkflowTable(
            name=data.name, workflow=data.workflow, created_by=created_by, company_id=company_id
        )

        await table.save()

        return WorkflowModel(**table.__dict__)

    async def check_workflow_exists(self, workflow_id: int) -> bool:
        """Checks if a workflow exists in the database.

        Args:
            workflow_id (int): The ID of the workflow to check.

        Returns:
            bool: True if the workflow exists, False otherwise.
        """
        return await WorkflowTable.exists(id=workflow_id, status=True)

    async def get_workflow_by_id(self, workflow_id: int) -> WorkflowModel:
        """Retrieves a workflow by its ID.

        Args:
            workflow_id (int): The ID of the workflow to retrieve.

        Returns:
            WorkflowModel: The retrieved workflow.

        Raises:
            InvalidOperationError: If no workflow with the given ID is found.
        """
        table = await WorkflowTable.get(id=workflow_id, status=True)
        return WorkflowModel(**table.__dict__, automations=[])

    async def get_workflows(
        self,
        query_params: BaseQueryParams,
        company_id: int,
    ) -> list[WorkflowModel]:
        """Retrieves a list of workflows.

        Args:
            query (str): A query string to filter workflows by name.
            company_id (int): The ID of the company to retrieve workflows for.
            offset (int): The offset to start retrieving workflows from.
            limit (int): The maximum number of workflows to retrieve.

        Returns:
            list[WorkflowModel]: A list of retrieved workflows.
        """
        # we need to search based on company_id or is_generated_true
        base_query = WorkflowTable.filter(
            Q(company_id=company_id) | Q(is_generated=True), status=True
        ).order_by(
            f"-{query_params.search_field}"
            if query_params.sort_by == SortBy.DESC
            else f"{query_params.search_field}"
        )

        if query_params.search_query:
            base_query = base_query.filter(name__icontains=query_params.search_query)
        query_result = (
            await base_query.offset(query_params.offset)
            .limit(query_params.limit)
            .values("id", "name")
        )
        return [WorkflowModel(**row) for row in query_result]

    async def update_workflow(
        self, workflow_id: int, company_id: int, data: CreateWorkflowModel
    ) -> bool:
        """Updates a workflow.

        Args:
            workflow_id (int): The ID of the workflow to update.
            company_id (int): The ID of the company the workflow belongs to.
            data (CreateWorkflowModel): The updated workflow data.

        Returns:
            bool: True if the workflow was updated successfully, False otherwise.

        Raises:
            InvalidOperationError: If no workflow with the given ID is found.
        """
        res = await WorkflowTable.filter(id=workflow_id, company_id=company_id, status=True).update(
            **data.model_dump()
        )

        if not res:
            raise InvalidOperationError(message=f"Workflow with id {workflow_id} not found")

        return res is not None

    async def delete_workflow(self, workflow_id: int, company_id: int) -> bool:
        """Deletes a workflow.

        Args:
            workflow_id (int): The ID of the workflow to delete.
            company_id (int): The ID of the company the workflow belongs to.

        Returns:
            bool: True if the workflow was deleted successfully, False otherwise.

        Raises:
            InvalidOperationError: If no workflow with the given ID is found.
        """
        res = await WorkflowTable.filter(id=workflow_id, company_id=company_id).update(status=False)

        if not res:
            raise InvalidOperationError(message=f"Workflow with id {workflow_id} not found")

        return res is not None
