import asyncio

from injector import inject, singleton
from redis.asyncio import Red<PERSON>
from snowflakekit import Snowf<PERSON>Generator
from taskiq import AsyncTaskiqTask

from src.app.core import logger
from src.app.core.exceptions.resources_exceptions import (
    InvalidOperationError,
    ResourceDuplicationError,
    ResourceNotFoundError,
)
from src.app.core.exceptions.user_exception import UserPermissionError
from src.app.db.tables.jd_enums import StageStr
from src.app.routers.graphql.jd.schemas.jd_schema import JDFilters, JDItems, JDSort, StageWithCount
from src.app.routers.jds.repositories.jd_repo import JDRepo
from src.app.routers.jds.repositories.jd_workflow_repo import JDWorkflowRepo
from src.app.routers.jds.schemas.apply_form_schemas import (
    ApplyFormJDModel,
    JDResponse,
    JDSearchResponseModel,
)
from src.app.routers.jds.schemas.jd_schemas import (
    CreateJDModel,
    JDEmbeddingModel,
    JDFullUpdateModel,
    JDModel,
    JDPartialUpdateModel,
    JDSearchQueryModel,
    JDStatusModel,
    LeaderBoardModel,
    UpdateActionEnum,
)
from src.app.tasks.jd_tasks import (
    create_jd_embedding_task,
    save_jd_to_graph_task,
    update_jd_task,
)

# from src.app.tasks.jd_tasks import create_jd_embedding_task, save_jd_to_graph_task
from src.app.utils.helpers.jd_id_creator import JDIDCreator
from src.app.utils.helpers.time_conversion import convert_to_days
from src.app.utils.pubsub.notification_center import NotificationType
from src.app.utils.pubsub.publisher import NotificationPublisher
from src.app.utils.repos.ai_repo import AIRepo
from src.app.utils.schemas.candidate_schema import JDStageInfoModel
from src.app.utils.schemas.user_schemas import AuthUserSchema, RolePermissionLevelStr
from src.app.utils.services.candidate_service import CandidateService
from src.app.utils.services.user_service import UserService


@singleton
class JDService:
    @inject
    def __init__(
        self,
        jd_repo: JDRepo,
        sid: SnowflakeGenerator,
        user_service: UserService,
        candidate_service: CandidateService,
        jd_workflow_repo: JDWorkflowRepo,
        redis: Redis,
        publisher: NotificationPublisher,
        ai_repo: AIRepo,
    ):
        self.jd_repo = jd_repo
        self.sid = sid
        self.user_service = user_service
        self.candidate_service = candidate_service
        self.jd_workflow_repo = jd_workflow_repo
        self.redis = redis
        self.publisher = publisher
        self.ai_repo = ai_repo

    async def create_jd(self, jd_data: CreateJDModel, user: AuthUserSchema) -> JDModel:
        """
        Creates a new JD (Job Description) in the system.

        Args:
            jd_data (CreateJDModel): The data for the new JD.
            user (AuthUserSchema): The user creating the JD.

        Returns:
            JDModel: The created JD.

        Raises:
            UserPermissionError: If the user does not have permission to create JDs.
            ResourceDuplicationError: If a JD with the same data already exists.
            InvalidOperationError: If the company notation is not found.
        """

        # check user role access
        if user.roles_responsibilities.jd_management != RolePermissionLevelStr.CREATE:
            raise UserPermissionError()
        if not await self.jd_repo.is_ready_to_save(jd_data, user.user_id):
            raise ResourceDuplicationError()

        jd_id = await self.sid.generate()  # jd primary-id
        # Must get the jd count today
        jd_count = await self.jd_repo.get_jd_count_today(user.company)
        # Get the company notation
        if not user.notation:
            raise InvalidOperationError(message="Company Not Found")

        hid = await JDIDCreator().create(user.notation, jd_count + 1)
        logger.info(f"Generated JD_ID {hid}")

        data = JDModel(
            jd_id=jd_id,
            hid=hid,
            company_id=user.company,
            created_by=user.user_id,
            **jd_data.model_dump(exclude_none=True),
        )

        # update notice periods
        data.notice_period_in_days = await convert_to_days(data.notice_period)

        # get the int user_id assignees
        assignees = await self.user_service.get_user_by_ids(jd_data.assignee_ids)
        data.assignee_ids = [str(assignee.id) for _, assignee in assignees.items()]

        # these save jds and assign jds need to be done in one transaction
        await self.jd_repo.create_jd_with_assignee(jd_data=data)
        # we need to wait until jds generated to generate embedding
        # start jd embedding job
        # Start Save JD to Graph Job that will be in separate services

        # now start two jobs
        await asyncio.gather(
            create_jd_embedding_task.kiq(data.model_dump()),
            # start other jobs
            save_jd_to_graph_task.kiq(data.model_dump()),
        )

        # remove company_id not need to expose to frontend
        data.company_id = 0
        data.created_by = 0
        data.assignee_ids = jd_data.assignee_ids

        # notify the user who is the creator of this jd
        sid = await self.sid.generate()
        await self.publisher.publish_notification(
            NotificationType.JD_CREATED,
            id=sid,
            user_id=user.user_id,
            user_name=user.name,
            company_id=user.company,
            jd_id=data.jd_id,
            jd_name=data.jd_name,
            meta={"assignees": [i.model_dump() for i in assignees.values()]},
        )

        # Notify the assignees(user id should be normal int id not snowflake id)
        notification_task = []
        for assignee_id, _ in assignees.items():
            notification_task.extend(
                [
                    self.publisher.publish_notification(
                        NotificationType.ASSIGNEE_CHANGE,
                        id=await self.sid.generate(),
                        parent_notification_id=sid,
                        user_id=assignee_id,
                        user_name=user.name,
                        company_id=user.company,
                        jd_id=str(data.jd_id),
                        jd_name=data.jd_name,
                        action="Assigned",
                    )
                ]
            )
        if notification_task:
            # send notification to all the assignees
            await asyncio.gather(*notification_task)

        return data

    async def get_jd_embedding(self, jd_id: int) -> JDEmbeddingModel:
        """
        Retrieves a job description embedding by its ID.

        Args:
            jd_id (int): The ID of the job description.

        Returns:
            JDEmbeddingModel: The job description embedding model.
        """
        result = await self.jd_repo.get_jd_embedding(
            jd_id, fields=["designation", "primary_skills", "secondary_skills"]
        )

        if result["jd_embedding"]:  # type: ignore
            return JDEmbeddingModel(**result["jd_embedding"])  # type: ignore
        # if no embedding found, generate one
        text = f"""
        Designation: {result.get("designation", "")}
        Skills: {(result.get("primary_skills", "") or "") +
                 (result.get("secondary_skills", "") or "")}
        """
        result = await self.generate_jd_embedding(text, jd_id)  # type: ignore
        if not result:
            raise ResourceNotFoundError()
        return JDEmbeddingModel(**result)

    async def generate_jd_embedding(self, text: str, jd_id: int) -> dict | None:
        """
        Generates a job description embedding using the provided text and updates the JD repository.

        Args:
            text (str): The input text to embed.
            jd_id (int): The ID of the job description.

        Returns:
            dict[Any, Any]: The generated job description embedding.
        """
        embedding = await self.ai_repo.fetch_embedding_from_api(text)
        if embedding:
            await self.jd_repo.update_jd_embedding(jd_id, embedding)
        return embedding

    async def update_jd(self, jd_id: int, jd_data: JDFullUpdateModel, user: AuthUserSchema) -> str:
        """
        Updates a job description in the repository.

        Args:
            jd_id (int): The ID of the job description to update.
            jd_data (JDFullUpdateModel): The updated job description data.
            user (AuthUserSchema): The user performing the update.

        Returns:
            str: A success message if the update is successful.

        Raises:
            UserPermissionError: If the user does not have sufficient
            permission to edit the job description.
        """
        # check user role access
        if user.roles_responsibilities.jd_management not in (
            RolePermissionLevelStr.CREATE,
            RolePermissionLevelStr.EDIT,
        ):
            raise UserPermissionError("You don't have sufficient permission. Role: JD Management")
        # check user has access or not
        if not await self.jd_repo.check_jd_user_access(jd_id, int(user.user_id)):
            raise UserPermissionError("You don't have sufficient permission to edit this JD")

        task: AsyncTaskiqTask = await update_jd_task.kiq(
            jd_id=jd_id, data=jd_data, current_user=user
        )
        logger.info(f"Update JD task {task.task_id} created for jd_id {jd_id}")
        return "Successful"

    async def patch_jd(self, jd_id: int, jd: JDPartialUpdateModel, user: AuthUserSchema) -> str:
        """
        Updates a job description based on the provided action and data.

        Args:
            jd_id (int): The ID of the job description to update.
            jd (JDPartialUpdateModel): The updated job description data.
            user (AuthUserSchema): The user performing the update.

        Returns:
            str: A success message if the update is successful.

        Raises:
            UserPermissionError: If the user does not have sufficient
            permission to edit the job description.
            InvalidOperationError: If the update operation is invalid.
        """
        # check user has access or not
        if not await self.jd_repo.check_jd_user_access(jd_id, user.user_id):
            raise UserPermissionError()
        logger.info(f"JD patch data for {jd.action.value} and jd_id {jd_id}")
        logger.debug(f"JD patch data {jd.model_dump(exclude_none=True)}")
        # Publish notification
        notification_task = None
        if jd.action != UpdateActionEnum.WITHDRAW:
            notification_task = self.publisher.publish_notification(
                NotificationType.JD_UPDATED,
                id=await self.sid.generate(),
                user_id=user.user_id,
                user_name=user.name,
                company_id=user.company,
                jd_id=str(jd_id),
                jd_name=jd.jd_name,
            )
        if jd.action == UpdateActionEnum.JD_STATUS:
            res = await self.jd_repo.update_jd_status(jd_id=jd_id, jd_data=jd)
            return "Successful" if res else "Failed"

        if jd.action == UpdateActionEnum.APPLICATION_FORM:
            res = await self.jd_repo.update_application_form(jd_id=jd_id, jd_data=jd)
            return "Successful" if res else "Failed"

        if jd.action == UpdateActionEnum.CAREER_PAGE:
            res = await self.jd_repo.update_career_form(jd_id=jd_id, jd_data=jd)
            return "Successful" if res else "Failed"

        if jd.action == UpdateActionEnum.WITHDRAW:
            res = await self.jd_repo.withdraw_from_jd(
                jd_id=jd_id,
                jd_data=jd,
                user_id=int(user.user_id),
                company_id=user.company,
            )
            await self.publisher.publish_notification(
                NotificationType.WITHDRAW_JD,
                id=await self.sid.generate(),
                user_id=user.user_id,
                user_name=user.name,
                company_id=user.company,
                jd_id=str(jd_id),
                jd_name=jd.jd_name,
                withdraw_reason=jd.comment,
                meta={"comment": jd.comment},
            )
            return "Successful" if res else "Failed"
        if notification_task:
            # send notification to all the assignees
            await notification_task
        raise InvalidOperationError()

    async def delete_jd(self, jd_id: int, user: AuthUserSchema) -> str:
        """
        Deletes a job description based on the provided ID.

        Args:
            jd_id (int): The ID of the job description to delete.
            user (AuthUserSchema): The user performing the deletion.

        Returns:
            str: A success message if the deletion is successful.
        """
        # check user has access or not
        if not await self.jd_repo.check_jd_user_access(jd_id, int(user.user_id)):
            raise UserPermissionError()

        status = await self.jd_repo.delete_jd(jd_id)
        return "Deleted" if status else "Failed"

    async def get_apply_form(self, jd_id: int) -> ApplyFormJDModel:
        """
        Retrieves the application form for a job description based on the provided ID.

        Args:
            jd_id (int): The ID of the job description for which to retrieve the application form.

        Returns:
            ApplyFormJDModel: The application form for the specified job description.
        """
        logger.debug(f"Apply form for jd_id {jd_id}")
        return await self.jd_repo.get_application_form(jd_id)

    async def get_jds(self, jd_ids: list[str]) -> list[JDResponse]:
        """
        Retrieves a list of job descriptions by their IDs.

        Args:
            jd_ids (list[str]): A list of job description IDs.

        Returns:
            list[JDResponse]: A list of job description responses.
        """
        jd_ids = [int(jd_id) for jd_id in jd_ids]  # type: ignore
        return await self.jd_repo.get_jds(jd_ids=jd_ids)  # type: ignore[arg-type]

    async def get_jds_gql(
        self,
        jd_ids: list[int] | None,
        user: AuthUserSchema,
        offset: int,
        limit: int,
        fields: list[str] | None = None,
        sub_fields: list[dict] | None = None,
        filters: JDFilters | None = None,
        sort_by: JDSort | None = None,
    ) -> tuple[list[JDItems], int]:
        """
        Fetches a JD by ID.

        Args:
            jd_id (str): The Snowflake ID of the JD to fetch.

        Returns:
            JDModel: The fetched JD, or None if no JD exists with the given ID.
        """

        jd_data, total_records = await self.jd_repo.get_jds_gql(
            user, offset, limit, jd_ids, fields, filters, sort_by
        )

        if sub_fields and jd_data:
            # get the sub field titles to check
            sub_filed_titles = [next(iter(field.keys())) for field in sub_fields]
            _jd_ids = jd_ids if jd_ids else [jd["jd_id"] for jd in jd_data]
            jd_items = []

            if "assignees" in sub_filed_titles or "withdrawn_assignees" in sub_filed_titles:
                jd_assignees = await self.jd_repo.get_jd_assignees(_jd_ids)
                withdrawn_assignees = await self.jd_repo.get_jd_withdrawn_assignees(_jd_ids)
                assignees_by_jd: dict[int, list[int]] = {}
                for assignee in jd_assignees:
                    jd_id = assignee["jd_id"]
                    if jd_id not in assignees_by_jd:
                        assignees_by_jd[jd_id] = []
                    # add assignee_id to the list based on the jd_id
                    assignees_by_jd[jd_id].append(assignee["assignee_id"])

                # get unique assignees from the list of dictionaries
                un_assignees_by_jd: dict[int, list[int]] = {}
                for assignee in withdrawn_assignees:
                    jd_id = assignee["jd_id"]
                    if jd_id not in un_assignees_by_jd:
                        un_assignees_by_jd[jd_id] = []
                    # add assignee_id to the list based on the jd_id
                    un_assignees_by_jd[jd_id].append(assignee["assignee_id"])

                unique_assignees = list(
                    {a["assignee_id"] for a in jd_assignees + withdrawn_assignees}
                )
                # get user details from user repository
                user_details = await self.user_service.get_user_by_ids(unique_assignees)

                for jd in jd_data:
                    jd_assignees = assignees_by_jd.get(jd["jd_id"], [])  # type: ignore
                    jd_assignee_details = [
                        user_details.get(int(assignee_id))  # type: ignore
                        for assignee_id in jd_assignees
                        if int(assignee_id) in user_details  # type: ignore
                    ]
                    jd_un_assignees = un_assignees_by_jd.get(jd["jd_id"], [])  # type: ignore
                    jd_un_assignee_details = [
                        user_details.get(int(assignee_id)).user_id  # type: ignore
                        for assignee_id in jd_un_assignees
                        if int(assignee_id) in user_details  # type: ignore
                    ]
                    jd_items.append(
                        JDItems(
                            **jd,
                            assignees=jd_assignee_details,
                            withdrawn_assignees=jd_un_assignee_details,
                        )
                    )

            if "stages_with_count" in sub_filed_titles:
                if not jd_items:
                    jd_items = [JDItems(**jd) for jd in jd_data]
                jds_stage_with_count = await self._get_jds_stage_with_count(_jd_ids)

                # NOTE: added ignore because mypy is not able to infer the strawberry model type
                for jd in jd_items:  # type: ignore
                    jd_id = int(jd.jd_id)  # type: ignore
                    if jd_id in jds_stage_with_count:
                        # Add the stages with their counts to the JD item
                        jd.stages_with_count = [  # type: ignore
                            StageWithCount(stage=k, count=v)  # type: ignore
                            for k, v in jds_stage_with_count[jd_id].items()  # type: ignore
                        ]

            return jd_items, total_records

        return [JDItems(**jd) for jd in jd_data], total_records

    async def _get_jds_stage_with_count(self, jd_ids: list[int]) -> dict[int, dict[str, int]]:
        """
        Retrieve stage information and counts for a list of JD IDs.

        This method fetches JD stage details(with count of candidates) from the candidate service
        and stage names from the JD workflow repository.

        Args:
            jd_ids (list[int]): A list of JD IDs for which to retrieve stage information.

        Returns:
            dict[int, dict[str, int]]: A dictionary mapping JD IDs to another dictionary
            of stage names and their respective counts.
        """
        # fetches JDs stage details(with count of candidates)
        jds_stage_info = await self.candidate_service.get_jd_stages_info(jd_ids)
        # fetches assigned workflow details for jds
        jds_workflows = await self.jd_workflow_repo.get_workflows_by_jd_ids(jd_ids)

        results = {}
        for jd_id in jd_ids:  # type: ignore[assignment]
            stage_info: JDStageInfoModel | None = jds_stage_info.get(jd_id)  # type: ignore[attr-defined]
            jd_workflow = jds_workflows.get(jd_id)
            if jd_workflow and "stage" in jd_workflow:
                # a dictionary to store the stage names and their counts, initialized to 0
                stages_with_count = {
                    str(stage["name"]).capitalize(): 0
                    for stage in
                    # FIXME: Default stages should be available while creating a workflow
                    [{"name": value} for value in StageStr.__members__] + jd_workflow["stage"]
                }
                if stage_info:
                    stages_with_count.update(
                        {k.capitalize(): v for k, v in stage_info.stages_with_cnt.items()}
                    )

                results[jd_id] = stages_with_count
        return results

    async def get_jds_assignees(self, jd_ids: list[str]):
        """
        Retrieves the assignees for a list of job descriptions.

        Args:
            jd_ids (list[str]): A list of job description IDs.

        Returns:
            list[dict]: A list of dictionaries containing the JD ID, assignee ID, and company ID.
        """
        _jd_ids = [int(jd_id) for jd_id in jd_ids]
        return await self.jd_repo.get_jd_assignees(_jd_ids)

    async def search_jds(
        self, query: JDSearchQueryModel
    ) -> tuple[list[JDSearchResponseModel], int]:
        """
        Search job descriptions by query.

        Args:
            query (JDSearchQueryModel): The search query.

        Returns:
            list[JDResponse]: The list of matching job descriptions.
        """
        return await self.jd_repo.search_jds(query)

    async def get_jds_for_dashboard(self, company_id: int) -> JDStatusModel:
        """
        Retrieves the count of job descriptions for a given company, categorized by status.

        Args:
            company_id (int): The ID of the company for
            which to retrieve job description statistics.

        Returns:
            JDStatusModel: An instance containing the counts of job descriptions for each status.
        """
        logger.info(f"Get jds for dashboard for company_id {company_id}")
        return await self.jd_repo.get_jds_for_dashboard(company_id)

    async def get_leaderboard(self, company_id: int) -> list[LeaderBoardModel]:
        """
        Retrieves the leaderboard for a given company.

        Args:
            company_id (int): The ID of the company for which to retrieve the leaderboard.

        Returns:
            list[LesderBoardModel]: The leaderboard, which is a list of
            dictionaries containing the user ID, name, and counts of active and closed JDs.
        """
        return await self.jd_repo.get_leaderboard(company_id)
