from abc import ABC, abstractmethod

from src.app.routers.jds.schemas.jd_workflow_schemas import CreateJDWorkflowModel
from src.app.routers.workflows.schemas.workflow_schemas import WorkflowModel


class AbstractJDWorkflowRepo(ABC):
    """Abstract base class for JD workflow repositories."""

    @abstractmethod
    async def update_or_create(
        self, jd_id: int, data: CreateJDWorkflowModel, user_id: int
    ) -> WorkflowModel:
        """Updates or creates a JD workflow.

        Args:
            jd_id(int): The ID of the JD.
            data (CreateJDWorkflowModel): The data for the JD workflow.
            user_id (int): The ID of the user who created or updated the workflow.

        Returns:
            A WorkflowModel object representing the updated or created JD workflow.

        Raises:
            NotImplementedError: This method is abstract and must be implemented by subclasses.
        """
        raise NotImplementedError

    @abstractmethod
    async def get_workflow_by_id(self, jd_id: int) -> WorkflowModel:
        """Retrieves a JD workflow by its ID.

        Args:
            jd_id: The ID of the JD.

        Returns:
            A WorkflowModel object representing the JD workflow, or None if not found.

        Raises:
            NotImplementedError: This method is abstract and must be implemented by subclasses.
        """
        raise NotImplementedError

    @abstractmethod
    async def get_workflows_by_jd_ids(self, jd_ids: list[int]) -> dict[int, dict]:
        """Retrieves a JD workflow by its ID.

        Args:
            jd_id: The ID of the JD.

        Returns:
            A WorkflowModel object representing the JD workflow, or None if not found.

        Raises:
            NotImplementedError: This method is abstract and must be implemented by subclasses.
        """
        raise NotImplementedError
