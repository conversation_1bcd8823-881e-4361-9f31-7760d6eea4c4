from fastapi import APIRouter, Depends, Request
from fastapi_injector import Injected

from src.app.core.auth.authentication import get_current_user
from src.app.routers.workflows.schemas.workflow_schemas import (
    CreateWorkflowModel,
    WorkflowModel,
)
from src.app.routers.workflows.services.workflow_service import WorkflowService
from src.app.utils.response_helper import success_response
from src.app.utils.schemas.filter_schema import BaseQueryParams
from src.app.utils.schemas.user_schemas import AuthUserSchema
from src.app.utils.swagger_helper import generate_swagger_responses


router = APIRouter(
    tags=["workflows"],
)


@router.post(
    "/v1/workflows",
    responses=generate_swagger_responses(WorkflowModel),
    summary="Create a new workflow",
    description="Creates a new workflow with the given name and stages/automations.",
    status_code=201,
)
async def create_workflow(
    request: Request,
    data: CreateWorkflowModel,
    service: WorkflowService = Injected(WorkflowService),
    user: AuthUserSchema = Depends(get_current_user),
):
    """
    Creates a new workflow.

    - **name**: The name of the workflow (required).
    - **workflow**: A dictionary representing the workflow structure,
       including stages and their associated automation IDs (required).
    """
    workflow = await service.create_workflow(
        data, company_id=user.company, created_by=int(int(user.user_id))
    )
    return success_response(workflow, request, status_code=201)


@router.get(
    "/v1/workflows/{workflow_id}",
    responses=generate_swagger_responses(WorkflowModel),  # Updated response model
    summary="Get a workflow by ID",
    description="Retrieves a workflow by its unique ID.",
)
async def get_workflow(
    request: Request,
    workflow_id: int,
    service: WorkflowService = Injected(WorkflowService),
    _=Depends(get_current_user),
):
    """
    Retrieves a workflow by its unique ID.

    - **workflow_id**: The ID of the workflow to retrieve.
    """
    workflow = await service.get_workflow_by_id(workflow_id)
    return success_response(workflow, request)


@router.delete(
    "/v1/workflows/{workflow_id}",
    responses=generate_swagger_responses(str),
    summary="Delete a workflow",
    description="Deletes a workflow by its unique ID.",
    status_code=204,  # Use 204 No Content for successful deletion
)
async def delete_workflow(
    request: Request,
    workflow_id: int,
    service: WorkflowService = Injected(WorkflowService),
    user: AuthUserSchema = Depends(get_current_user),
):
    """
    Deletes a workflow by its unique ID.

    - **workflow_id**: The ID of the workflow to delete.
    """
    res = await service.delete_workflow(workflow_id, user.company)
    return success_response(res, request)


@router.put(
    "/v1/workflows/{workflow_id}",
    responses=generate_swagger_responses(str),
    summary="Update a workflow",
    description="Updates a workflow by its unique ID.",
)
async def update_workflow(
    request: Request,
    workflow_id: int,
    data: CreateWorkflowModel,
    service: WorkflowService = Injected(WorkflowService),
    user: AuthUserSchema = Depends(get_current_user),
):
    """
    Updates a workflow by its unique ID.

    - **workflow_id**: The ID of the workflow to update.
    - **data**: The updated workflow data.
    """
    updated_workflow = await service.update_workflow(workflow_id, user.company, data)
    return success_response(updated_workflow, request)


@router.get(
    "/v1/workflows",
    responses=generate_swagger_responses(list[WorkflowModel]),  # Return a list
    summary="Get all workflows",
    description="Retrieves a list of workflows with optional filtering and pagination.",
)
async def get_all_workflows(
    request: Request,
    query_params: BaseQueryParams = Depends(),
    service: WorkflowService = Injected(WorkflowService),
    user: AuthUserSchema = Depends(get_current_user),
):
    """
    Retrieves a list of workflows.

    - **query**: Optional search query to filter workflows by name.
    - **offset**: Pagination offset for retrieving results.
    - **limit**: Maximum number of workflows to return.
    """
    workflows = await service.get_workflows(query_params, company_id=user.company)
    return success_response(workflows, request)
