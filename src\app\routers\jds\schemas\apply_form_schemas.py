from datetime import date
from enum import Enum
from typing import Any

import orjson
from pydantic import BaseModel, Field, model_validator

from src.app.db.tables.jd_enums import JDStatusType, JobType, PresenceType, SalaryDurationType
from src.app.routers.jds.schemas.jd_enums import JobStrType, PresenceStrType, SalaryDurationStrType


class ApplyFormJDModel(BaseModel):
    jd_name: str = Field(
        ...,
        min_length=5,
        max_length=255,
        description="JD name",
    )
    jd_text: str = Field(
        ...,
        min_length=5,
        description="JD text",
    )
    designation: str = Field(
        ...,
        min_length=3,
        max_length=255,
        description="JD Designation",
    )
    min_work_exp: int = Field(
        ...,
        gt=0,
        lt=50,
        description="min work exp",
    )
    max_work_exp: int | None = Field(
        ...,
        description="min work exp",
    )
    job_type: JobStrType = Field(
        ...,
        description="Job Type",
    )
    job_preference: PresenceStrType = Field(
        ...,
        description="Job Type",
    )
    primary_skills: str = Field(
        ...,
        min_length=1,
        description="List of Skills",
    )
    secondary_skills: str | None = Field(None, description="List of Secondary Skills")
    preferred_location: str = Field(
        ...,
        min_length=1,
        max_length=255,
        description="Preferred location",
    )
    company_name: str = Field(
        ...,
        min_length=1,
        max_length=255,
        description="Preferred location",
    )
    company_url: str | None = Field(
        None,
        min_length=1,
        max_length=255,
        description="Company URL",
    )
    notice_period: str = Field(
        ...,
        min_length=1,
        max_length=255,
        description="Notice period",
    )
    salary_currency: str | None = Field(
        None,
        min_length=3,
        max_length=3,
        description="Currency code for salary",
    )
    min_salary: float | None = Field(
        None,
        description="Minimum salary",
    )
    max_salary: float | None = Field(
        None,
        description="Max salary",
    )
    salary_duration: SalaryDurationStrType | None = Field(
        None,
        description="Salary duration",
    )
    hourly_rate: str | None = Field(None, description="Hourly rate for contract type")
    confirmed_start_date: date | None = Field(
        None, description="Confirmed start date for contract type"
    )
    bill_to_client: str | None = Field(None, description="Bill to client for contract type")
    contract_acceptance: bool | None = Field(
        None, description="Contract acceptance for contract type"
    )

    application_form: dict | None = Field(None, description="Application form")


class JDResponse(ApplyFormJDModel):
    hid: str
    jd_id: str
    jd_status: str
    created_by: int
    company_id: int

    @model_validator(mode="before")
    @classmethod
    def update_enum_values(cls, values: dict[str, Any]) -> dict[str, Any]:
        for field, value in values.items():
            if isinstance(value, Enum):
                values[field] = value.name
            elif field == "jd_id":
                values[field] = str(value)
            elif field == "jd_status" and isinstance(value, int):
                values[field] = JDStatusType(value).name
            elif field == "job_type" and isinstance(value, int):
                values[field] = JobType(value).name
            elif field == "job_preference" and isinstance(value, int):
                values[field] = PresenceType(value).name
            elif field == "salary_duration" and isinstance(value, int):
                values[field] = SalaryDurationType(value).name
            elif field == "application_form" and isinstance(value, str):
                values[field] = orjson.loads(value)
        return values


class JDSearchResponseModel(BaseModel):
    designation: str
    hid: str
    company_name: str
    location: str
    notice_period: str
    skills: str
    years_of_exp: int
