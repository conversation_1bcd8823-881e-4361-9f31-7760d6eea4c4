from typing import Any

from injector import inject, singleton
from pydantic import BaseModel
from redis.asyncio import Redis

from src.app.core import logger

from .notification_center import NotificationCenter, NotificationType, PublishChannel


@singleton
class NotificationPublisher:
    @inject
    def __init__(self, redis_client: Redis, notification_center: NotificationCenter):
        self.redis_client = redis_client
        self.notification_center = notification_center

    async def publish_notification(self, notification_type: NotificationType, **kwargs) -> int:
        """
        Publish a notification using a predefined template

        Args:
            notification_type: Type of notification to publish
            **kwargs: Data to include in the notification

        Returns:
            Number of subscribers who received the message
        """
        notification = self.notification_center.create_notification(notification_type, **kwargs)
        channel = notification["channel"]
        data: BaseModel = notification["model"](**notification["data"])

        data_js = data.model_dump_json()
        subscribers_count = await self.redis_client.publish(channel, data_js)

        logger.info(
            f"Data Published to {channel} for {notification_type}. Subscribers: {subscribers_count}"
        )
        return subscribers_count

    async def publish_custom(self, channel: PublishChannel, data: Any) -> int:
        """
        Publish custom data to a channel

        Args:
            channel: Channel to publish to
            data: Data model to publish

        Returns:
            Number of subscribers who received the message
        """
        data_js = data.model_dump_json() if isinstance(data, BaseModel) else data
        subscribers_count = await self.redis_client.publish(channel, data_js)

        logger.info(f"Custom data published to {channel}. Subscribers: {subscribers_count}")
        return subscribers_count
