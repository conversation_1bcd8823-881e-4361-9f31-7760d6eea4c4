from src.app.routers.workflows.repoitories.abstract_workflow_repo import AbstractWorkflowRepo
from src.app.routers.workflows.schemas.workflow_schemas import CreateWorkflowModel, WorkflowModel
from src.app.utils.schemas.filter_schema import BaseQueryParams


class FakeWorkflowRepo(AbstractWorkflowRepo):
    async def create_workflow(
        self,
        data: CreateWorkflowModel,
        created_by: int,
        company_id: int,
    ) -> WorkflowModel:
        return WorkflowModel(
            id=1,
            name="Sample Workflow",
            workflow={"stage": [], "version": 1},
            automations=[],
        )

    async def check_workflow_exists(self, workflow_id: int) -> bool:
        return True

    async def get_workflow_by_id(self, workflow_id: int) -> WorkflowModel:
        return WorkflowModel(
            id=1,
            name="Sample Workflow",
            workflow={"stage": [], "version": 1},
            automations=[],
        )

    async def get_workflows(
        self,
        query_params: BaseQueryParams,
        company_id: int,
    ) -> list[WorkflowModel]:
        return [
            WorkflowModel(
                id=1,
                name="Sample Workflow",
                workflow={"stage": [], "version": 1},
                automations=[],
            )
        ]

    async def update_workflow(
        self, workflow_id: int, company_id: int, data: CreateWorkflowModel
    ) -> bool:
        return True

    async def delete_workflow(self, workflow_id: int, company_id: int) -> bool:
        return True
