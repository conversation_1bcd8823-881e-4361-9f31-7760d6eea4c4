from abc import ABC, abstractmethod
from typing import Any

from src.app.routers.jds.schemas.user_schema import AssigneeSchema


class AbstractUserRepo(ABC):
  @abstractmethod
  async def fetch_users_from_api(self, user_ids: list[int]) -> list[AssigneeSchema]:
    raise NotImplementedError
  
  @abstractmethod
  async def invalidate_user_cache(self, cache_key: str):
    raise NotImplementedError
  
  @abstractmethod
  async def update_user_cache(self, users: list[AssigneeSchema]):
    raise NotImplementedError
  
  @abstractmethod
  async def get_cached_users(self, user_ids) -> tuple[list[AssigneeSchema], Any]:
    raise NotImplementedError
  
  
  
