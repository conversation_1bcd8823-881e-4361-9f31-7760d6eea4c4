from injector import singleton
from tortoise.expressions import Q

from src.app.core.exceptions.resources_exceptions import InvalidOperationError
from src.app.db.tables.workflow_tables import ScenarioTable
from src.app.routers.scenarios.repositories.abstract_scenario_repo import AbstractScenarioRepo
from src.app.routers.scenarios.schemas.scenario_schemas import CreateScenarioModel, ScenarioModel


@singleton
class ScenarioRepo(AbstractScenarioRepo):
    """Class for accessing scenario data in database"""

    async def create_scenario(
        self,
        data: CreateScenarioModel,
        company_id: int,
        created_by: int,
    ) -> ScenarioModel:
        """Create scenario data in database

        Args:
            data (CreateScenarioModel): Data for creating scenario
            company_id (int): Company id
            created_by (int): User id who created scenario

        Returns:
            ScenarioModel: Scenario data
        """
        data = data.model_dump(exclude_unset=True, exclude_none=True)
        table = ScenarioTable(**data)
        table.company_id = company_id
        table.created_by = created_by
        await table.save()
        return ScenarioModel(**table.__dict__)

    async def get_scenarios(
        self,
        query: str | None,
        company_id: int,
        offset: int,
        limit: int,
    ) -> list[ScenarioModel]:
        """Get scenarios from database

        Args:
            query (str): Search query
            company_id (int): Company id
            offset (int): Offset
            limit (int): Limit

        Returns:
            list[ScenarioModel]: Scenario list
        """
        base_query = ScenarioTable.filter(
            Q(company_id=company_id) | Q(is_generated=True), status=True
        ).order_by("-modified_at")
        if query:
            base_query = base_query.filter(category__icontains=query)

        table = await base_query.offset(offset).limit(limit)

        return [ScenarioModel(**row.__dict__) for row in table]

    async def update_scenario(
        self, scenario_id: int, company_id: int, data: CreateScenarioModel
    ) -> ScenarioModel | None:
        """Update scenario data in database

        Args:
            scenario_id (int): Scenario id
            company_id (int): Company id
            data (CreateScenarioModel): Data for updating scenario

        Raises:
            InvalidOperationError: Raise error when scenario not found

        Returns:
            ScenarioModel | None: Scenario data or None
        """
        data = data.model_dump(exclude_unset=True, exclude_none=True)
        res = await ScenarioTable.filter(id=scenario_id, company_id=company_id).update(**data)
        if not res:
            raise InvalidOperationError(message=f"Scenario with id {scenario_id} not found")
        return ScenarioModel(**data, id=scenario_id)

    async def delete_scenario(self, scenario_id: int, company_id: int, created_by: int) -> bool:
        """Delete scenario from database

        Args:
            scenario_id (int): Scenario id
            company_id (int): Company id
            created_by (int): User id

        Raises:
            InvalidOperationError: Raise error when scenario not found
            or user does not have permission

        Returns:
            bool: True if scenario is deleted else False
        """
        res = await ScenarioTable.filter(
            id=scenario_id, company_id=company_id, created_by=created_by
        ).update(status=False)

        if not res:
            raise InvalidOperationError(
                message=f"Scenario with id {scenario_id} not found or "
                f"you don't have permission to delete it"
            )
        return res is not None
