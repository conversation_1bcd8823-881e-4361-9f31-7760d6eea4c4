from enum import StrEnum

from pydantic import BaseModel, Field


class RolePermissionLevelStr(StrEnum):
    NONE = "NONE"
    VIEW = "VIEW"
    EDIT = "EDIT"
    CREATE = "CREATE"


class RoleResponsibilities(BaseModel):
    dashboard: RolePermissionLevelStr = RolePermissionLevelStr.NONE
    user_management: RolePermissionLevelStr = RolePermissionLevelStr.NONE
    jd_management: RolePermissionLevelStr = RolePermissionLevelStr.NONE
    candidate_management: RolePermissionLevelStr = RolePermissionLevelStr.NONE
    communication_module: RolePermissionLevelStr = RolePermissionLevelStr.NONE
    ai_assistant: RolePermissionLevelStr = RolePermissionLevelStr.NONE
    analytics: RolePermissionLevelStr = RolePermissionLevelStr.NONE
    settings: RolePermissionLevelStr = RolePermissionLevelStr.NONE
    credits: RolePermissionLevelStr = RolePermissionLevelStr.NONE
    vendor_management: RolePermissionLevelStr = RolePermissionLevelStr.NONE


class AuthUserSchema(BaseModel):
    name: str
    uid: str
    role: str
    company: int
    company_name: str
    db_id: int
    user_id: int = Field(alias="db_id", description="The db_id as internal id for user")
    notation: str
    email: str
    email_verified: bool
    iss: str
    aud: str
    auth_time: int
    roles_responsibilities: RoleResponsibilities


class AuthType(StrEnum):
    USER = "USER"
    SERVICE = "SERVICE"
    BOTH = "BOTH"
