from fastapi import <PERSON><PERSON><PERSON>

# from redis import asyncio as aioredis
from tortoise.contrib.fastapi import register_tortoise

from src.app.core.config.settings import get_settings
from src.app.db.tables.jd_constants import JD_TABLES


TORTOISE_ORM = {
    "connections": {
        "master": {
            "engine": "tortoise.backends.asyncpg",
            "credentials": {
                "host": get_settings().POSTGRES_HOST,
                "port": get_settings().POSTGRES_PORT,
                "user": get_settings().POSTGRES_USER,
                "password": get_settings().POSTGRES_PASSWORD,
                "database": get_settings().POSTGRES_DB,
                # choose schema
                "schema": "jd",
                # Minimum connection pool size
                "minsize": 1,
                # Maximum connection pool size
                "maxsize": 5,
                # Connection timeout
                "max_inactive_connection_lifetime": 300,
                # Maximum number of queries before reconnecting
                "max_queries": 50000,
            },
        },
    },  # postgresql
    # "connections": {"default": "mysql://root:123456@127.0.0.1:3306/test"},
    # "connections": {"default": "sqlite://:memory:"},
    # "connections": {"default": "sqlite://./podcast.db"},
    "routers": ["src.app.db.router.Router"],
    "apps": {
        "models": {
            "models": [*JD_TABLES, "aerich.models"],
            "default_connection": "master",
        },
    },
}


def setup_db(app: FastAPI):
    """Initializes the Tortoise ORM for the FastAPI application.
    Args:
        app (FastAPI): The FastAPI application instance.

    """
    # init tortoise orm
    register_tortoise(
        app,
        config=TORTOISE_ORM,
        generate_schemas=False,
        add_exception_handlers=False,
    )
