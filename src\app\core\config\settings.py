import logging
import os
from functools import lru_cache

from pydantic import ConfigDict
from pydantic_settings import BaseSettings


LOGGER_NAME = os.getenv("LOGGER_NAME", "JD_SERVICE")
logger = logging.getLogger(LOGGER_NAME)


class Settings(BaseSettings):
    """
    Define settings for the application including app name,
    version, mode, log level, CORS configuration, database configuration,
    Redis configuration, email configuration, and Celery configuration.
    """

    APP_NAME: str = "JD SERVICE"
    APP_VERSION: str = "0.0.1"
    ENVIRONMENT: str
    LOG_LEVEL: int
    SERVER_NAME: str

    #################################
    ###### CORS Config ##############
    #################################
    ORIGINS: str
    ALLOWED_HOST: str

    #################################
    ###### Database Config ##########
    #################################
    POSTGRES_HOST: str | None
    POSTGRES_USER: str | None
    POSTGRES_PASSWORD: str | None
    POSTGRES_DB: str | None
    POSTGRES_PORT: str | None

    #################################
    ###### REDIS Config ##########
    #################################
    REDIS_HOST: str
    REDIS_PORT: int

    ###############################
    # OPEN AI
    ###############################
    OPENAI_API_KEY: str

    #################################
    ###### sentry Config ############
    #################################
    SENTRY_DSN: str

    ##################
    # OTHER SERVICES #
    ##################
    USER_SERVICE: str
    RANKING_SERVICE: str
    CANDIDATE_SERVICE: str
    AI_SERVICE: str


    ###############################################
    ################### firebase ##################
    ###############################################
    FIREBASE_CONFIG: str

    ########################################
    # INTER SERVER COMMUNICATION AUTH CREDS
    ########################################
    JWT_SECRET_KEY: str
    JWT_ALGORITHM: str
    JWT_IDENTIFIER: str

    model_config = ConfigDict(extra="ignore")


# @lru_cache
# def get_settings() -> Settings:
#     """
#     Retrieve and return the application settings.
#     If ENVIRONMENT and SERVER_NAME exist in the environment variables,
#     load settings from the Docker environment. Otherwise, load settings
#     from the local .env file.
#     Returns:
#         Settings: An instance of the Settings class containing
#         application configurations.
#     """
#     if os.getenv("ENVIRONMENT") and os.getenv("SERVER_NAME"):
#         return Settings()

#     return Settings(
#         _env_file="./.env",
#     )

# I Commented for the sake of CI/CD
# If you want to use the local .env file, uncomment the above code and

@lru_cache
def get_settings() -> Settings:
    """
    Retrieve and return the application settings.
    
    Load `.env.ci` during CI pipelines.
    Load settings from environment in Docker if ENVIRONMENT and SERVER_NAME are set.
    Otherwise, load from local .env.
    """
    if os.getenv("CI") == "true":
        # For GitHub Actions
        return Settings(_env_file=".env.ci")

    if os.getenv("ENVIRONMENT") and os.getenv("SERVER_NAME"):
        # For Docker or prod environments
        return Settings()

    # Default for local development
    return Settings(_env_file=".env")
