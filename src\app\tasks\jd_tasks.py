from datetime import datetime, <PERSON><PERSON><PERSON>

from fastapi_injector import InjectedTaskiq
from pydantic import BaseModel

from src.app.routers.jds.schemas.jd_schemas import JDFullUpdateModel
from src.app.routers.jds.schemas.ranking_schema import JDRankingModel
from src.app.tasks.services.task_service import TaskService
from src.app.utils.schemas.graph_db_schema import JDSchema
from src.app.utils.schemas.user_schemas import AuthUserSchema
from src.app.utils.services.graph_service import RankingService
from src.app.worker import broker, redis_source


@broker.task(task_name="save_jd_to_graph_task", retry_on_error=True, max_retries=3)
async def save_jd_to_graph_task(
    data: dict,
    service: RankingService = InjectedTaskiq(RankingService),
):
    """Saves a job description to the graph database."""

    if isinstance(data, BaseModel):
        data = data.model_dump()

    skills = ""
    if "primary_skills" in data:
        skills = data["primary_skills"]
    if data.get("secondary_skills"):
        skills += f",{data['secondary_skills']}"

    jd_id = int(data["jd_id"])
    designation = data.get("designation", None)

    request_data: JDSchema = JDSchema(
        jd_id=jd_id, skills=skills.split(","), designation=designation
    )  # type: ignore[no-redef]
    return await service.add_jd_to_graph(request_data)


@broker.task(task_name="Re_run_ranking_task", retry_on_error=True, max_retries=3)
async def re_run_ranking_task(
    jd_id: int,
    user: dict,
    service: TaskService = InjectedTaskiq(TaskService),
):
    """
    Re-runs the ranking task for a given job description ID.

    Args:
        jd_id (int): The ID of the job description to re-run ranking for.
        service (TaskService): The task service to use. Defaults to InjectedTaskiq(TaskService).

    Returns:
        The result of the re-run ranking process.
    """
    return await service.run_ranking(jd_id, user)


@broker.task(task_name="update_jd_task", retry_on_error=True, max_retries=3)
async def update_jd_task(
    jd_id: int,
    data: JDFullUpdateModel,
    current_user: AuthUserSchema,
    service: TaskService = InjectedTaskiq(TaskService),
):
    # Updates a job description in the database graph.
    jd_data = data.model_dump()
    jd_data.update({"jd_id": jd_id})
    await save_jd_to_graph_task.kiq(data=jd_data)
    # only re-run the ranking if jds [skills,designation,experience] has changed
    if service.jd_repo.is_ranking_data_changed(JDRankingModel(jd_id=jd_id, **data.model_dump())):
        await re_run_ranking_task.schedule_by_time(
            source=redis_source,
            time=datetime.now() + timedelta(seconds=15),
            jd_id=jd_id,
            # JWT-specific fields ('auth_time', 'aud', 'iss') to avoid validation conflicts
            user=current_user.model_dump(exclude={"auth_time", "aud", "iss"}),
        )
    return await service.update_jd(jd_id, data, current_user)


@broker.task(task_name="create_jd_embedding_task", retry_on_error=True, max_retries=3)
async def create_jd_embedding_task(
    data: dict,
    service: TaskService = InjectedTaskiq(TaskService),
):
    """Creates a job description embedding using OpenAI's embedding model and
       saves it to the database.

    Args:
        data (JDModel): The job description data to be processed.
        service (TaskService, optional): The task services to use.


    Returns:
        str: The created job description embedding.
    """
    return await service.generate_jd_embedding(data)


# @broker.task(task_name="send fcm notification", retry_on_error=True, max_retries=3)
# async def send_fcm_notification_task(
#     notification_type: NotificationType,
#     data:dict,
#     publisher: NotificationPublisher = InjectedTaskiq(NotificationPublisher),
# ):
#     """Sends a FCM notification to the specified user."""

#     await publisher.publish_notification(notification_type, **data)
