from pydantic import BaseModel, ConfigDict, Field


class JDRankingModel(BaseModel):
    jd_id: int = Field(..., description="Job description ID")
    designation: str = Field(..., description="Designation of the candidate")
    primary_skills: str = Field(..., description="Primary skills of the candidate, comma-separated")
    secondary_skills: str | None = Field(
        None, description="Secondary skills of the candidate,comma-separated"
    )
    min_work_exp: int = Field(..., description="Experience required for the job description")
    max_work_exp: int | None = Field(
        None, description="Experience required for the job description"
    )

    model_config = ConfigDict(extra="ignore")
