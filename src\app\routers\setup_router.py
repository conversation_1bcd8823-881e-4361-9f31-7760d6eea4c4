from fastapi import FastAPI

from src.app.routers.graphql.router import graphql_app
from src.app.routers.jds import router as jd_router
from src.app.routers.ping import router as ping_router
from src.app.routers.scenarios import router as scenario_router
from src.app.routers.workflows import router as workflow_router


def init_routes(app: FastAPI):
    app.include_router(jd_router.router)
    app.include_router(scenario_router.router)
    app.include_router(workflow_router.router)
    app.include_router(ping_router.router)
    # crud router
    app.include_router(graphql_app, prefix="/gql")
