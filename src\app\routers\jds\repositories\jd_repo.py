import re
from datetime import datetime
from typing import Any

from injector import inject, singleton
from redis.asyncio import Redis
from tortoise import Tortoise
from tortoise.expressions import Q
from tortoise.functions import Count
from tortoise.transactions import in_transaction

from src.app.core import logger
from src.app.db.tables.jd_enums import (
    DepartmentType,
    JDStatusType,
    JobType,
    PresenceType,
    PriorityType,
    SalaryDurationType,
)
from src.app.db.tables.jd_tables import JDAssignTable, JDTable
from src.app.routers.graphql.jd.schemas.jd_schema import (
    JDFilters,
    JDSort,
    JDTaskStatusEnum,
    SortOrder,
)
from src.app.routers.jds.repositories.abstract_jd_repo import AbstractJDRepo
from src.app.routers.jds.repositories.queries import Queries
from src.app.routers.jds.schemas.apply_form_schemas import (
    ApplyFormJDModel,
    JDResponse,
    JDSearchResponseModel,
)
from src.app.routers.jds.schemas.jd_schemas import (
    CreateJDModel,
    JDFullUpdateModel,
    JDModel,
    JDPartialUpdateModel,
    JDSearchQueryModel,
    JDStatusModel,
    LeaderBoardModel,
)
from src.app.routers.jds.schemas.ranking_schema import JDRankingModel
from src.app.utils.caches.jd_cache_management import JDCacheManagement
from src.app.utils.schemas.user_schemas import AuthUserSchema


def contains_space_or_special_char(text) -> bool:
    pattern = r'[\s!@#$%^&*()_+\-=\[\]{};\':"\\|,.<>/?]'
    return bool(re.search(pattern, text))


@singleton
class JDRepo(AbstractJDRepo):
    @inject
    def __init__(self, cache: JDCacheManagement, redis: Redis, queries: Queries):
        self.cache = cache
        self.redis = redis
        self.queries = queries

    async def get_jd_count_today(self, company_id: int) -> int:
        return await JDTable.filter(
            company_id=company_id,
            created_at__gte=datetime.now().replace(hour=0, minute=0, second=0, microsecond=0),
        ).count()

    async def get_jd_embedding(self, jd_id: int, fields: list | None = None) -> dict:
        if fields:
            fields.append("jd_embedding")
        else:
            fields = ["jd_embedding"]
        return await JDTable.get(jd_id=jd_id).values(*fields)

    async def convert_to_table(self, jd: JDModel | JDFullUpdateModel) -> dict:
        data = jd.model_dump(
            exclude_unset=True, exclude=["assignee_ids", "created_by_details", "assignees"]
        )
        # as we are create first time, jd_status make it archive
        data.update(
            {
                "hid": jd.hid,
                "jd_status": JDStatusType[jd.jd_status].value,
                "hiring_priority": PriorityType[jd.hiring_priority].value,
                "job_type": JobType[jd.job_type].value,
                "job_preference": PresenceType[jd.job_preference].value,
                "department": DepartmentType[jd.department].value if jd.department else None,
            }
        )
        if jd.salary_duration:
            data["salary_duration"] = SalaryDurationType[jd.salary_duration.upper()].value
        return data

    async def check_jd_user_access(self, jd_id: int, user_id: int) -> bool:
        """
        Checks if a user has access to a JD. If not, returns False.
        Args:
            jd_id: Requested JD.
            user_id: Requested user.

        Returns:
            bool: True if the user has access to the JD, False otherwise.
        """
        res = await self.cache.get_assignee(jd_id, user_id)
        if res == "Not Found":
            # hit the database it expired
            data = await JDAssignTable.filter(jd_id=jd_id, is_unassigned=False).values(
                "assignee_id", "assignor_id"
            )
            created_user = await JDTable.get(jd_id=jd_id).values("created_by")
            ids = [id for i in data for id in (i["assignee_id"], i["assignor_id"])]  # noqa: A001
            await self.cache.add_created_by(jd_id, created_user["created_by"])
            await self.cache.set_assignee(jd_id, ids)
            return user_id in ids

        return res == "Assigned"

    async def is_ready_to_save(self, jd_data: CreateJDModel, user_id: int) -> bool:
        data_hash = str(
            hash(
                str(jd_data.jd_name + jd_data.designation + jd_data.primary_skills)
                + f"user_id:{user_id}"
            )
        )

        if await self.redis.setnx(data_hash, 1):
            # Key didn't exist, set it with 2-minute expiry
            await self.redis.expire(data_hash, 120)
            return True

        return False

    async def create_jd_with_assignee(self, jd_data: JDModel):
        async with in_transaction():
            # create jds first
            await self.create_jd(jd_data)
            await self.crate_assign_users(
                jd_id=int(jd_data.jd_id),
                assignee_ids=jd_data.assignee_ids,
                company_id=jd_data.company_id,
                assigned_by=jd_data.created_by,
            )

    async def create_jd(self, jd_data: JDModel) -> JDModel:
        data = await self.convert_to_table(jd_data)
        table = JDTable(**data)
        await table.save()
        await self.cache.set_jd(table.jd_id, jd_data, table.created_by)
        return jd_data

    async def update_jd(self, jd_id: int, jd_data: JDFullUpdateModel) -> JDModel:
        data = await self.convert_to_table(jd_data)
        data.pop("hid")
        await JDTable.filter(jd_id=jd_id).update(**data)
        await self.cache.set_jd(jd_id, jd_data)
        return jd_data

    def _skills_changed(self, existing_skills: str | None, new_skills: str | None) -> bool:
        existing_set = {s.strip().lower() for s in (existing_skills or "").split(",") if s.strip()}
        new_set = {s.strip().lower() for s in (new_skills or "").split(",") if s.strip()}
        return existing_set != new_set

    async def is_ranking_data_changed(self, jd_ranking_data: JDRankingModel) -> bool:
        existing_jd_table = await JDTable.get(jd_id=jd_ranking_data.jd_id).only(
            "designation", "primary_skills", "secondary_skills", "min_work_exp", "max_work_exp"
        )
        return (
            existing_jd_table.designation.strip() != jd_ranking_data.designation.strip()
            or self._skills_changed(
                existing_jd_table.primary_skills, jd_ranking_data.primary_skills
            )
            or self._skills_changed(
                existing_jd_table.secondary_skills, jd_ranking_data.secondary_skills
            )
            or existing_jd_table.min_work_exp != jd_ranking_data.min_work_exp
            or existing_jd_table.max_work_exp != jd_ranking_data.max_work_exp
        )

    async def un_assign_to_jd(self, jd_id: int, company_id: int, user_ids: list[int]) -> bool:
        res = await JDAssignTable.filter(
            jd_id=jd_id, company_id=company_id, assignee_id__in=user_ids
        ).update(is_unassigned=True, status=False)
        if res:
            await self.cache.delete_assignee(jd_id, user_ids)
        return res is not None

    async def update_jd_assignee(
        self, jd_id: int, jd_data: JDFullUpdateModel, company_id: int, user_id: int
    ) -> tuple[list[int], list[int]]:
        # check all the assignee for current jd_id
        # if any user id not available in jd_data.assignee_ids
        # then that user will be unassigned
        assignee_ids = [int(assignee_id) for assignee_id in jd_data.assignee_ids]
        # Fetch current assignees from the database
        existing_assignees = await JDAssignTable.filter(
            jd_id=jd_id,
            company_id=company_id,
        ).values("assignee_id", "is_unassigned", "is_withdrawn")

        need_unassign = []  # type: ignore
        need_assign = []
        existing_assignee_ids = [user["assignee_id"] for user in existing_assignees]
        new_assignee_ids = list(set(assignee_ids) - set(existing_assignee_ids))

        for i in existing_assignees:
            # if user_id is available in assignee_ids also he is not unassigned or withdrawn
            # and he is assigned till now then skip
            if (
                i["assignee_id"] in assignee_ids
                and not i["is_unassigned"]
                and not i["is_withdrawn"]
            ):
                continue
            if i["assignee_id"] in assignee_ids:
                need_assign.append(i["assignee_id"])
            else:
                need_unassign.append(i["assignee_id"])

        logger.info(f"need_unassign: {need_unassign}, new_assignee_ids: {new_assignee_ids}")
        async with in_transaction():
            # need assign
            if need_assign:
                await JDAssignTable.filter(
                    jd_id=jd_id,
                    assignee_id__in=need_assign,
                ).update(is_unassigned=False, is_withdrawn=False, status=True)

            if need_unassign:
                await self.un_assign_to_jd(
                    jd_id=jd_id, company_id=company_id, user_ids=need_unassign
                )

            # Add new assignees
            if new_assignee_ids:
                await self.crate_assign_users(
                    assigned_by=user_id,
                    jd_id=jd_id,
                    assignee_ids=new_assignee_ids,
                    company_id=company_id,
                )

        return need_assign + new_assignee_ids, need_unassign

    async def update_jd_embedding(self, jd_id: int, embedding: dict) -> bool:
        res = await JDTable.filter(jd_id=jd_id).update(jd_embedding=embedding)
        return res is not None

    async def get_jds(self, jd_ids: list[int]) -> list[JDResponse]:
        jds = await JDTable.filter(jd_id__in=jd_ids).all()
        return [JDResponse(**jd.__dict__) for jd in jds]

    async def get_jds_gql(
        self,
        user: AuthUserSchema,
        offset: int,
        limit: int,
        jd_ids: list | None = None,
        fields: list[str] | None = None,
        filters: JDFilters | None = None,
        sort_by: JDSort | None = None,
    ) -> tuple[list[dict], int]:
        # Return only those peoples assigned JDs
        query = await self.apply_filters_jd(user, filters)
        if jd_ids:
            query = query.filter(jd_id__in=jd_ids)
        if sort_by and sort_by.field and sort_by.order:
            order_by = f"{'-' if sort_by.order == SortOrder.DESC else ''}{sort_by.field.value}"
            query = query.order_by(order_by)
        logger.debug(f"query for jd -> {query.sql()}")
        total_records = await query.count()
        if fields:
            if "is_workflow_created" in fields:
                # use reverse relationship to get is_workflow_created
                query = query.annotate(is_workflow_created=Count("workflow_jd"))
                # Ensure 'is_workflow_created' is included in the final field list
                fields = [f for f in fields if f != "is_workflow_created"] + ["is_workflow_created"]
            query = query.offset(offset).limit(limit)
            return await query.values(*fields), total_records
        query = query.offset(offset).limit(limit)
        return await query.all().values(), total_records

    async def apply_filters_jd(self, user: AuthUserSchema, filters: JDFilters | None):
        base_query = JDTable.filter(company_id=user.company, status=True).order_by("-modified_at")

        # If the jd in "Draft" status, then it should be visible to only the creator of the jd
        base_query = base_query.filter(
            Q(jd_status__not=JDStatusType.DRAFT) | Q(created_by=user.user_id)
        )

        async def get_allowed_jd_ids():
            return await JDAssignTable.filter(
                assignee_id=user.user_id, is_unassigned=False, is_withdrawn=False, status=True
            ).values_list("jd_id", flat=True)

        if filters is None:  # if not filters.assignees must return created by and assigned user.
            allowed_jd_ids = await get_allowed_jd_ids()
            return base_query.filter(Q(jd_id__in=allowed_jd_ids) | Q(created_by=user.user_id))

        if filters.assignees:
            query = await self.filter_by_assignees(user, filters.assignees)
        else:
            allowed_jd_ids = await get_allowed_jd_ids()
            query = base_query.filter(Q(jd_id__in=allowed_jd_ids) | Q(created_by=user.user_id))

        if filters.jd_name:
            # jd_name may contain jd_id
            if not contains_space_or_special_char(filters.jd_name):
                query = query.filter(
                    Q(jd_name__icontains=filters.jd_name) | Q(hid__icontains=filters.jd_name)
                )
            else:
                query = query.filter(jd_name__icontains=filters.jd_name)

        if filters.location:
            location_filter = Q()
            for location in filters.location:
                location_filter |= Q(preferred_location__icontains=location)
            query = query.filter(location_filter)

        if filters.job_type:
            query = query.filter(job_type__in=[i.value for i in filters.job_type])  # type: ignore[attr-defined]

        if filters.status:
            query = query.filter(jd_status__in=[i.value for i in filters.status])  # type: ignore[attr-defined]
        if filters.job_preference:
            query = query.filter(
                job_preference__in=[
                    i.value  # type: ignore[attr-defined]
                    for i in filters.job_preference
                ]
            )

        if filters.date_range and filters.date_range.start_date and filters.date_range.end_date:
            query = query.filter(
                created_at__gte=filters.date_range.start_date,
                created_at__lte=filters.date_range.end_date,
            )

        return query

    async def filter_by_assignees(self, user: AuthUserSchema, assignees: list[JDTaskStatusEnum]):
        query = None
        for assignee in assignees:
            if assignee == JDTaskStatusEnum.ASSIGNED_TO_ME:
                jds = await self.get_jd_by_assignees(int(user.user_id))
                query = JDTable.filter(jd_id__in=jds, company_id=user.company, status=True)
            elif assignee == JDTaskStatusEnum.CREATED_BY_ME:
                query = JDTable.filter(
                    created_by=user.user_id, company_id=user.company, status=True
                )
            elif assignee == JDTaskStatusEnum.ASSIGNED_TO_TEAM:
                jds = await self.get_jd_by_assignors(user.user_id)
                query = JDTable.filter(jd_id__in=jds, company_id=user.company, status=True)
            # elif assignee == JDTaskStatusEnum.CREATED_BY_TEAM:
            #     user_ids = []
            #     query = JDTable.filter(created_by__in=user_ids, company_id=user.company_id,
            # status=True)
        return query

    async def get_jd_assignees(self, jd_ids: list[int]) -> list[dict]:
        return await JDAssignTable.filter(
            jd_id__in=jd_ids, is_unassigned=False, is_withdrawn=False, status=True
        ).values("jd_id", "assignee_id", "company_id")

    async def get_jd_withdrawn_assignees(self, jd_id: list[int]) -> list[dict]:
        return await JDAssignTable.filter(jd_id__in=jd_id, is_withdrawn=True, status=True).values(
            "assignee_id", "company_id", "jd_id"
        )

    async def search_jds(
        self, search_query: JDSearchQueryModel
    ) -> tuple[list[JDSearchResponseModel], int]:
        is_designation = bool(search_query.designation)
        is_skills = bool(search_query.skills)
        is_exp = bool(search_query.year_of_exp)
        query = self.queries.build_jd_search_query(is_skills, is_designation, is_exp)
        params: list[Any] = []
        if is_skills:
            params.append(tuple(search_query.skills.split(",")))  # type: ignore
        if is_designation:
            params.append(search_query.designation)  # type: ignore
        if is_exp:
            params.append(search_query.year_of_exp)  # type: ignore
        params.append(search_query.company_id)  # type: ignore
        # Only fetch the active jds
        params.append(JDStatusType.ACTIVE)
        logger.debug(f"query -> {query} --> params: {params}")
        connection = Tortoise.get_connection("master")
        total_records, jds = await connection.execute_query(query, params)
        logger.info(f"GOt No.of jd search {total_records}")
        return [JDSearchResponseModel(**dict(jd)) for jd in jds], total_records

    async def delete_jd(self, jd_id: int) -> bool:
        res = await JDTable.filter(jd_id=jd_id).update(status=False)
        await self.cache.delete_jd(jd_id)
        return res is not None

    async def crate_assign_users(
        self,
        assigned_by: int,
        jd_id: int,
        assignee_ids: list[str] | list[int],
        company_id: int,
    ) -> None:
        """Assigns a job description to a list of users.

        Args:
            assigned_by (int): The ID of the user assigning the job description.
            jd_id (int): The ID of the job description.
            assignee_ids (list[int]): The list of IDs of the users to assign the job description to.
            company_id: int

        Returns:
            bool: True if the job description was successfully assigned to the users,
            False otherwise.

        Raises:
            None
        """
        await JDAssignTable.bulk_create(
            [
                JDAssignTable(
                    jd_id=jd_id,
                    assignor_id=assigned_by,
                    assignee_id=int(user),
                    company_id=company_id,
                )
                for user in assignee_ids
            ],
            ignore_conflicts=True,
        )

    async def get_application_form(self, jd_id: int) -> ApplyFormJDModel:
        """
        Updates a JD.
        Args:
            jd_id (int): The ID of the JD to update.
        Returns:
             updated the JD.
        """
        data = ApplyFormJDModel.model_json_schema()
        fields = data["properties"].keys()
        res = await JDTable.get(jd_id=jd_id).values(*fields)
        res.update(
            {
                "job_type": res["job_type"].name,
                "job_preference": res["job_preference"].name,
                "salary_duration": res["salary_duration"].name if res["salary_duration"] else None,
            }
        )
        return ApplyFormJDModel(**res)

    async def update_application_form(self, jd_id: int, jd_data: JDPartialUpdateModel) -> bool:
        """
        Updates a JD.
        Args:
            jd_data:
            jd_id (int): The ID of the JD to update.
        Returns:
             updated the JD.
        """
        res = await JDTable.filter(jd_id=jd_id).update(application_form=jd_data.application_form)
        return res is not None

    async def update_career_form(self, jd_id: int, jd_data: JDPartialUpdateModel) -> bool:
        """
        Updates a JD.
        Args:
            jd_data:
            jd_id (int): The ID of the JD to update.
        Returns:
             updated the JD.
        """
        res = await JDTable.filter(jd_id=jd_id).update(career_page=jd_data.career_page)
        return res is not None

    async def update_jd_status(self, jd_id: int, jd_data: JDPartialUpdateModel) -> bool:
        """
        Updates a JD.
        Args:
            jd_data:
            jd_id (int): The ID of the JD to update.
        Returns:
             updated the JD.
        """
        if not jd_data.jd_status:
            return False

        res = await JDTable.filter(jd_id=jd_id).update(
            jd_status=JDStatusType[jd_data.jd_status].value
        )
        return res is not None

    async def withdraw_from_jd(
        self, jd_id: int, jd_data: JDPartialUpdateModel, user_id: int, company_id: int
    ) -> bool:
        """
        Updates a JD.
        Args:
            company_id:
            user_id:
            jd_data:
            jd_id (int): The ID of the JD to update.
        Returns:
             updated the JD.
        """
        res = await JDAssignTable.filter(
            jd_id=jd_id, assignee_id=user_id, company_id=company_id
        ).update(comment=jd_data.comment, is_withdrawn=True)
        await self.cache.delete_assignee(jd_id, [user_id])
        return res is not None

    async def get_jd_by_assignees(self, user_id: int) -> list[int]:
        return await JDAssignTable.filter(
            assignee_id=user_id, status=True, is_unassigned=False, is_withdrawn=False
        ).values_list("jd_id", flat=True)

    async def get_jd_by_assignors(self, user_id: int) -> list[int]:
        return await JDAssignTable.filter(
            Q(assignor_id=user_id) & ~Q(assignee_id=user_id) & Q(status=True)
            # & Q(is_unassigned=False)
            # & Q(is_withdrawn=False)
        ).values_list("jd_id", flat=True)

    async def get_jds_for_dashboard(self, company_id: int) -> JDStatusModel:
        """
        Retrieves the count of job descriptions for a given company, categorized by status.

        This method executes a query to fetch the number of job descriptions in various statuses
        (e.g., active, closed, draft, archived) for a specified company ID. The results are returned
        as a JDStatusModel, which provides a structured representation of the counts.

        Args:
            company_id (int): The ID of the company for which to
            retrieve job description statistics.

        Returns:
            JDStatusModel: An instance containing the counts of job descriptions for each status.
        """

        jd_counts = await Tortoise.get_connection("master").execute_query_dict(
            self.queries.get_jds_count(), [company_id]
        )
        return JDStatusModel(
            **{
                f"{status.name.lower()}_jds": next(
                    (row["count"] for row in jd_counts if row["jd_status"] == status), 0
                )
                for status in JDStatusType
            }
        )

    async def get_leaderboard(self, company_id: int) -> list[LeaderBoardModel]:
        """
        Retrieves the leaderboard for a given company.

        Args:
            company_id (int): The ID of the company for which to retrieve the leaderboard.

        Returns:
            list[LeaderBoardModel]: The leaderboard, which is a list of
            dictionaries containing the user ID, name, and counts of active and closed JDs.
        """
        query = self.queries.build_get_leaderboard()
        results = await Tortoise.get_connection("master").execute_query_dict(query, [company_id])
        return [LeaderBoardModel(**result) for result in results]
