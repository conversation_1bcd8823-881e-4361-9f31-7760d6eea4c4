name: CI - Tests, Coverage, and Formatting

on:
  pull_request:
    branches: ["main"]
    types: [opened, synchronize, reopened]
  push:
    branches: ["main"]

jobs:
  test-and-coverage:
    runs-on: ubuntu-latest

    steps:
    # -------------------------------
    # 🔁 Code Checkout
    # -------------------------------
    - name: Checkout code
      uses: actions/checkout@v3
      with:
        fetch-depth: 0

    # -------------------------------
    # 🐍 Setup Python
    # -------------------------------
    - name: Setup Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.12.4'

    # -------------------------------
    # 💾 Cache pip packages
    # -------------------------------
    - name: Cache pip packages
      uses: actions/cache@v4
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-

    # -------------------------------
    # 🔧 System dependencies
    # -------------------------------
    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y libpoppler-cpp-dev

    # -------------------------------
    # 📦 Install Python dependencies
    # -------------------------------
    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r docker/requirements.txt
        pip install pytest pytest-cov pre-commit

    # -------------------------------
    # 🧪 Setup Test Environment (.env.ci)
    # -------------------------------
    - name: Load .env.ci into environment
      run: |
        echo "CI=true" >> $GITHUB_ENV
        while IFS='=' read -r key value; do
          if [[ ! -z "$key" ]]; then
            echo "$key=$value" >> $GITHUB_ENV
          fi
        done < .env.ci

    # -------------------------------
    # 🧪 Create dummy Firebase config if needed
    # -------------------------------
    - name: Create dummy Firebase config (if FIREBASE_CONFIG is path)
      run: |
        if [[ ! -z "$FIREBASE_CONFIG" && "$FIREBASE_CONFIG" != "{}" && "$FIREBASE_CONFIG" != "" ]]; then
          mkdir -p $(dirname "$FIREBASE_CONFIG")
          echo '{}' > "$FIREBASE_CONFIG"
        fi

    # -------------------------------
    # 🔍 Detect changed files
    # -------------------------------
    - name: Detect changed files
      id: changed-files
      run: |
        changed_files=$(git diff --name-only ${{ github.event.before }} ${{ github.sha }} | wc -l)
        echo "changed_files=$changed_files" >> $GITHUB_OUTPUT
        echo "skip=$([ "$changed_files" -eq 0 ] && echo true || echo false)" >> $GITHUB_OUTPUT

    # -------------------------------
    # 🧹 Run pre-commit
    # -------------------------------
    - name: Run pre-commit
      if: steps.changed-files.outputs.skip == 'false'
      run: |
        pre-commit run --all-files > pre-commit-report.txt || true

    - name: Create mock Firebase service account
      run: |
        mkdir -p /tmp/firebase
        cat <<EOF > /tmp/firebase/service_account.json
        ******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        EOF
        echo "FIREBASE_CONFIG=/tmp/firebase/service_account.json" >> $GITHUB_ENV


    # -------------------------------
    # 🧪 Run tests with coverage
    # -------------------------------
    - name: Run tests with coverage
      if: steps.changed-files.outputs.skip == 'false'
      run: |
        pytest src/tests \
          --cov=src \
          --cov-config=.coveragerc \
          --cov-report=term \
          --cov-report=term-missing \
          --cov-report=html \
          --cov-report=xml > pytest-report.txt || true

    # -------------------------------
    # 📤 Upload test and coverage artifacts
    # -------------------------------
    - name: Upload test and coverage artifacts
      if: steps.changed-files.outputs.skip == 'false'
      uses: actions/upload-artifact@v4
      with:
        name: reports
        path: |
          pre-commit-report.txt
          pytest-report.txt
          htmlcov
          coverage.xml

    # -------------------------------
    # 🧠 Analyze test + coverage results
    # -------------------------------
    - name: Analyze test results and coverage
      if: steps.changed-files.outputs.skip == 'false'
      id: check-results
      run: |
        errors=()
        percent_coverage="N/A"

        if grep -q "FAILED" pytest-report.txt; then
          errors+=("❌ Some pytest tests failed.")
        fi

        if grep -q "failed" pre-commit-report.txt; then
          errors+=("❌ Pre-commit checks failed.")
        fi

        if [ -f coverage.xml ]; then
          raw_coverage=$(grep -Po 'line-rate="\K[0-9.]+' coverage.xml | head -n 1 || echo "0")
          percent_coverage=$(awk "BEGIN { printf \"%.2f\", $raw_coverage * 100 }")
        else
          percent_coverage="0.00"
        fi

        echo "✅ Test Coverage: ${percent_coverage}%"
        echo "coverage_percentage=$percent_coverage" >> $GITHUB_OUTPUT

        if [ ${#errors[@]} -gt 0 ]; then
          echo "errors<<EOF" >> $GITHUB_OUTPUT
          printf "%s\n" "${errors[@]}" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT
          exit 1
        fi

    # -------------------------------
    # 🗨️ Generate PR comment
    # -------------------------------
    - name: Generate PR comment
      if: github.event_name == 'pull_request' && steps.changed-files.outputs.skip == 'false'
      run: |
        lines_changed=$(git diff --shortstat ${{ github.event.before }} ${{ github.sha }})
        insertions=$(echo "$lines_changed" | grep -oP '\d+(?= insertions?)' || echo "0")
        deletions=$(echo "$lines_changed" | grep -oP '\d+(?= deletions?)' || echo "0")
        percent_coverage="${{ steps.check-results.outputs.coverage_percentage }}"

        echo "|  Metric         |  Value                                 |" > pr-comment.txt
        echo "|-----------------|------------------------------------------|" >> pr-comment.txt
        echo "| Test Coverage   | ${percent_coverage}%                     |" >> pr-comment.txt
        echo "| Code Changes    | Insertions: ${insertions}, Deletions: ${deletions} |" >> pr-comment.txt
        echo "| Summary         | Code touched in this PR                 |" >> pr-comment.txt

    # -------------------------------
    # 💬 Post PR comment
    # -------------------------------
    - name: Post PR comment
      if: github.event_name == 'pull_request'
      uses: marocchino/sticky-pull-request-comment@v2
      with: 
        path: pr-comment.txt

    # -------------------------------
    # 🧨 Report errors clearly
    # -------------------------------
    - name: Report Errors
      if: failure() && steps.check-results.outputs.errors
      run: |
        echo "❌ CI failed with the following issues:"
        echo "${{ steps.check-results.outputs.errors }}"
        exit 1
