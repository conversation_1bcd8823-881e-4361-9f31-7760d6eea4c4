from injector import inject, singleton

from src.app.utils.repos.candidate_repo import CandidateRepository
from src.app.utils.schemas.candidate_schema import JDStageInfoModel


@singleton
class CandidateService:
    """Provides methods for retrieving user data, utilizing a cache."""

    @inject
    def __init__(self, candidate_repo: CandidateRepository):
        """
        Initializes UserService.

        Args:
            user_repository: Repository for user data operations.
        """
        self.candidate_repo = candidate_repo

    async def get_jd_stages_info(self, jd_ids: list[int]) -> dict[int, JDStageInfoModel]:
        """ """
        cache_data: list[dict]
        not_found_ids: list[int]
        cache_data, not_found_ids = await self.candidate_repo.get_cached_data_list(
            "jd_stage", jd_ids
        )

        data_list: list[JDStageInfoModel] = [JDStageInfoModel(**data) for data in cache_data]

        cache_data_len = len(cache_data)
        if not_found_ids:
            data: list[JDStageInfoModel] = await self.candidate_repo.fetch_jds_stage_info_from_api(
                not_found_ids
            )
            data_list += data

        await self.candidate_repo.update_cache(
            cache_key_prefix="jd_stage",
            data_list=cache_data[cache_data_len:],
            data_ids=not_found_ids,
        )

        # Return a dictionary of with jd_ids and JDStageInfoModel
        return {data.jd_id: data for data in data_list}
