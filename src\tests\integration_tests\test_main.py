from starlette.testclient import TestClient

from src.app.core.auth.authentication import get_current_user
from src.app.main import app, injector
from src.app.routers.jds.repositories.jd_repo import JDRepo
from src.app.routers.jds.repositories.jd_workflow_repo import JDWorkflowRepo
from src.app.routers.scenarios.repositories.automation_repo import AutomationRepo
from src.app.routers.scenarios.repositories.scenario_repo import ScenarioRepo
from src.app.routers.workflows.repoitories.workflow_repo import WorkflowRepo
from src.app.utils.repos.user_repo import UserRepository
from src.app.utils.schemas.user_schemas import (
    AuthUserSchema,
    RolePermissionLevelStr,
    RoleResponsibilities,
)
from src.tests.fake_data.fake_automation_repo import FakeAutomationRepo
from src.tests.fake_data.fake_jd_repo import FakeJdRepo
from src.tests.fake_data.fake_jd_workflow_repo import FakeJdWorkflowRepo
from src.tests.fake_data.fake_scenario_repo import FakeScenarioRepo
from src.tests.fake_data.fake_user_repo import FakeUserRepo
from src.tests.fake_data.fake_workflow_repo import FakeWorkflowRepo


client = TestClient(app)
injector.binder.bind(JDRepo, to=FakeJdRepo())
injector.binder.bind(JDWorkflowRepo, to=FakeJdWorkflowRepo())
injector.binder.bind(ScenarioRepo, to=FakeScenarioRepo())
injector.binder.bind(AutomationRepo, to=FakeAutomationRepo())
injector.binder.bind(WorkflowRepo, to=FakeWorkflowRepo())
injector.binder.bind(UserRepository, to=FakeUserRepo())


def override_current_user():
    return AuthUserSchema(
        name="Test User",
        uid="1234",
        role="admin",
        company=1,
        company_name="test company",
        db_id="1",
        user_id=7,
        notation="HR",
        email="<EMAIL>",
        email_verified=True,
        iss="https://www.googleapis.com/oauth2/v3/certs",
        aud="1234",
        auth_time=0,
        roles_responsibilities=RoleResponsibilities(
            dashboard=RolePermissionLevelStr.CREATE,
            user_management=RolePermissionLevelStr.CREATE,
            jd_management=RolePermissionLevelStr.CREATE,
            candidate_management=RolePermissionLevelStr.CREATE,
            communication_module=RolePermissionLevelStr.CREATE,
            ai_assistant=RolePermissionLevelStr.CREATE,
            analytics=RolePermissionLevelStr.CREATE,
            settings=RolePermissionLevelStr.CREATE,
            credits=RolePermissionLevelStr.CREATE,
        ),
    )


app.dependency_overrides[get_current_user] = override_current_user
