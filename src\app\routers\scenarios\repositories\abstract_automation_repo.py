from abc import ABC, abstractmethod

from src.app.db.tables.workflow_tables import AutomationTable
from src.app.routers.scenarios.schemas.automation_schemas import (
    AutomationModel,
    CreateAutomationModel,
)


class AbstractAutomationRepo(ABC):
    """Abstract class for Automation repository."""

    @abstractmethod
    async def convert_table_to_model(self, table: AutomationTable) -> AutomationModel:
        """Convert AutomationTable object to AutomationModel object.

        Args:
            table (AutomationTable): The AutomationTable object.

        Raises:
            NotImplementedError: This method is not implemented.
        """
        raise NotImplementedError

    @abstractmethod
    async def create_automation(
        self, data: CreateAutomationModel, company_id: int, created_by: int, scenario_id: int
    ) -> AutomationModel:
        """Create a new automation.

        Args:
            data (CreateAutomationModel): The automation data.
            company_id (int): The company id.
            created_by (int): The user id who created the automation.
            scenario_id (int): The scenario id.

        Raises:
            NotImplementedError: This method is not implemented.
        """
        raise NotImplementedError

    @abstractmethod
    async def get_by_scenario_id(
        self,
        scenario_id: int,
        offset: int,
        limit: int,
    ) -> list[AutomationModel]:
        """Get automation by scenario id.

        Args:
            scenario_id (int): The scenario id.
            offset (int): The offset.
            limit (int): The limit.

        Raises:
            NotImplementedError: This method is not implemented.
        """
        raise NotImplementedError

    @abstractmethod
    async def get_automation(self, automation_id: int, company_id: int) -> AutomationModel:
        """Get automation by id.

        Args:
            automation_id (int): The automation id.
            company_id (int): The company id.

        Raises:
            NotImplementedError: This method is not implemented.
        """
        raise NotImplementedError

    @abstractmethod
    async def delete_automation(self, automation_id: int, company_id: int) -> str:
        """Delete automation by id.

        Args:
            automation_id (int): The automation id.
            company_id (int): The company id.

        Raises:
            NotImplementedError: This method is not implemented.
        """
        raise NotImplementedError

    @abstractmethod
    async def update_automation(
        self, automation_id: int, company_id: int, scenario_id: int, data: CreateAutomationModel
    ) -> AutomationModel:
        """Update automation by id.

        Args:
            automation_id (int): The automation id.
            company_id (int): The company id.
            scenario_id (int): The scenario id.
            data (CreateAutomationModel): The automation data.

        Raises:
            NotImplementedError: This method is not implemented.
        """
        raise NotImplementedError

    @abstractmethod
    async def get_automations(
        self,
        automation_ids: list[int],
    ) -> list[AutomationModel]:
        """Get multiple automations by ids.

        Args:
            automation_ids (List[int]): The list of automation ids.

        Raises:
            NotImplementedError: This method is not implemented.
        """
        raise NotImplementedError
