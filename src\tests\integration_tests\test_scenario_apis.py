import pytest

from src.app.core.auth.server_to_server_auth import ServerToServerAuth
from src.app.routers.scenarios.schemas.scenario_schemas import CreateScenarioModel
from src.tests.integration_tests.test_main import client, override_current_user


test_user = override_current_user()
auth_service = ServerToServerAuth()


@pytest.mark.asyncio
async def test_post_scenarios():
    jd_data = {"category": "Empty", "description": "This category does't have any description"}
    response = client.post("/v1/scenarios", json=jd_data)
    assert response.status_code == 200
    assert response.json()["data"]["category"] == "Empty"


@pytest.mark.asyncio
async def test_get_scenarios():
    response = client.get("/v1/scenarios")
    assert response.status_code == 200
    assert len(response.json()["data"]) > 0
    assert "category" in response.json()["data"][0]
    assert "description" in response.json()["data"][0]


@pytest.mark.asyncio
async def test_delete_scenarios():
    scenario_id = 1
    response = client.delete(f"/v1/scenarios/{scenario_id}")
    assert response.status_code == 200
    assert response.json()["data"] == "success"


@pytest.mark.asyncio
async def test_put_scenarios():
    scenario_id = 1
    jd_data = {"category": "John", "description": "Bad Boy"}
    scenario_model = CreateScenarioModel(**jd_data)
    response = client.put(f"/v1/scenarios/{scenario_id}", json=jd_data)
    assert response.status_code == 200
    assert response.json()["data"]["category"] == scenario_model.category
    assert response.json()["data"]["description"] == scenario_model.description


@pytest.mark.asyncio
async def test_post_scenarios_automations():
    scenario_id = 1
    data = {
        "description": "lorem ipsum dolor sit amet, consectetur adip",
        "template_id": 1,
        "channel_type": "EMAIL",
        "email_type": "PERSONAL",
        "schedule_type": "IMMEDIATELY",
        "schedule_time": "05:04:11.932Z",
    }
    response = client.post(f"/v1/scenarios/{scenario_id}/automations", json=data)
    assert response.status_code == 200
    assert response.json()["data"]["automation_id"] == 1


@pytest.mark.asyncio
async def test_get_scenarios_automations():
    scenario_id = 1
    response = client.get(f"/v1/scenarios/{scenario_id}/automations")
    assert response.status_code == 200
    automations = response.json()["data"]
    expected_automation = {
        "automation_id": 1,
        "description": "Sample automation",
        "template_id": 1,
        "channel_type": "EMAIL",
        "email_type": "PERSONAL",
        "schedule_type": "IMMEDIATELY",
        "schedule_time": "08:00:00.000000Z",
    }
    assert automations == [expected_automation]


@pytest.mark.asyncio
async def test_delete_scenarios_automations():
    scenario_id = 1
    automation_id = 1
    response = client.delete(f"/v1/scenarios/{scenario_id}/automations/{automation_id}")
    assert response.status_code == 200
    assert response.json()["data"] == "success"


@pytest.mark.asyncio
async def test_put_scenario_automations():
    scenario_id = 1
    automation_id = 1
    data = {
        "description": "Sample automation",
        "template_id": 1,
        "channel_type": "EMAIL",
        "email_type": "PERSONAL",
        "schedule_type": "IMMEDIATELY",
        "schedule_time": "05:32:54.847Z",
    }
    response = client.put(f"/v1/scenarios/{scenario_id}/automations/{automation_id}", json=data)
    assert response.status_code == 200
    assert response.json()["data"]["description"] == "Sample automation"


@pytest.mark.asyncio
async def test_get_automations():
    automation_id = 1
    response = client.get(f"/v1/automations?automation_id={automation_id}")
    assert response.status_code == 200
    assert len(response.json()["data"]) > 0


@pytest.mark.asyncio
async def test_post_scenarios_validation():
    jd_data = {}
    response = client.post("/v1/scenarios", json=jd_data)
    assert response.status_code == 422
    assert response.json()["error"]["code"] == "VALIDATION_ERROR"


@pytest.mark.asyncio
async def test_delete_scenarios_validation():
    scenario_id = "Jack"
    response = client.delete(f"/v1/scenarios/{scenario_id}")
    assert response.status_code == 422
    assert response.json()["error"]["code"] == "VALIDATION_ERROR"


@pytest.mark.asyncio
async def test_put_scenarios_validation():
    scenario_id = "abc"
    jd_data = {"category": 123, "description": "Bad Boy"}
    response = client.put(f"/v1/scenarios/{scenario_id}", json=jd_data)
    assert response.status_code == 422
    assert response.json()["error"]["code"] == "VALIDATION_ERROR"


@pytest.mark.asyncio
async def test_post_scenarios_automations_validation():
    scenario_id = "abc"
    data = {}
    response = client.post(f"/v1/scenarios/{scenario_id}/automations", json=data)
    assert response.status_code == 422
    assert response.json()["error"]["code"] == "VALIDATION_ERROR"


@pytest.mark.asyncio
async def test_get_scenarios_automations_validation():
    scenario_id = "Jack"
    response = client.get(f"/v1/scenarios/{scenario_id}/automations")
    assert response.status_code == 422
    assert response.json()["error"]["code"] == "VALIDATION_ERROR"


@pytest.mark.asyncio
async def test_delete_scenarios_automations_validation():
    scenario_id = "Jack"
    automation_id = "Jack"
    response = client.delete(f"/v1/scenarios/{scenario_id}/automations/{automation_id}")
    assert response.status_code == 422
    assert response.json()["error"]["code"] == "VALIDATION_ERROR"


@pytest.mark.asyncio
async def test_put_scenario_automations_validation():
    scenario_id = "abc"
    automation_id = "abc"
    data = {}
    response = client.put(f"/v1/scenarios/{scenario_id}/automations/{automation_id}", json=data)
    assert response.status_code == 422
    assert response.json()["error"]["code"] == "VALIDATION_ERROR"


@pytest.mark.asyncio
async def test_get_automations_validation():
    automation_id = "Jack"
    response = client.get(f"/v1/automations?automation_id={automation_id}")
    assert response.status_code == 422
    assert response.json()["error"]["code"] == "VALIDATION_ERROR"
