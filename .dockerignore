# IDE Configs
.vscode/
.idea/

# Python Environment
venv/
.venv/
__pycache__/
*.pyc
*.pyo
*.pyd
*.pyc

# Environment Variables
.env
.env.*

# Docker
docker-compose.override.yml
**/Dockerfile.local
.dockerignore
docker-volume-data/
docker-data/
*.log

# macOS
.DS_Store

# Optional: NodeJS (if frontend included)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Database / Storage Files
*.sqlite3
*.db

# Byte-compiled / Cache / Test
*.egg
*.egg-info/
.eggs/
dist/
build/
htmlcov/
.coverage
.tox/
.nox/
.cache/
pytest_cache/

# VS Code debugging log
*.code-workspace
