from injector import inject, singleton
from redis.asyncio import Redis

from src.app.routers.scenarios.repositories.automation_repo import (
    AutomationRepo,
)
from src.app.routers.workflows.repoitories.workflow_repo import WorkflowRepo
from src.app.routers.workflows.schemas.workflow_schemas import CreateWorkflowModel, WorkflowModel


@singleton
class WorkflowService:
    """
    WorkflowService provides methods for managing workflows.

    Attributes:
        repo (WorkflowRepo): The workflow repository instance.
        automation_repo (AutomationRepo): The automation repository instance.

    """

    @inject
    def __init__(self, repo: WorkflowRepo, redis: Redis, automation_repo: AutomationRepo):
        self.repo = repo
        self.automation_repo = automation_repo
        self.redis = redis

    async def get_workflow_by_id(self, workflow_id: int) -> WorkflowModel:
        """
        Retrieves a workflow by its ID, including associated automations.

        Args:
            workflow_id: The ID of the workflow.

        Returns:
            The WorkflowModel object representing the retrieved workflow.

        """
        data = await self.repo.get_workflow_by_id(workflow_id)
        if not data.workflow:
            return data

        # get automations
        ids = []

        for i in data.workflow["stage"]:
            ids.extend(i["automations"])

        unique_ids = list(set(ids))
        data.automations = await self.automation_repo.get_automations(unique_ids)

        return data

    async def get_workflows(
        self,
        query_params,
        company_id: int,
    ) -> list[WorkflowModel]:
        """
        Retrieves a list of workflows, optionally filtered by a query.

        Args:
            query: Search string for filtering workflows by name.
            company_id: The ID of the company to retrieve workflows for.
            offset: Pagination offset.
            limit: Pagination limit.

        Returns:
            A list of WorkflowModel objects representing the retrieved workflows.

        """
        return await self.repo.get_workflows(query_params, company_id)

    async def create_workflow(
        self, data: CreateWorkflowModel, created_by: int, company_id: int
    ) -> WorkflowModel:
        """
        Creates a new workflow.

        Args:
            data: The workflow data.
            created_by: The ID of the user who created the workflow.
            company_id: The ID of the company to create the workflow for.

        Returns:
            The WorkflowModel object representing the created workflow.

        """
        return await self.repo.create_workflow(data, created_by, company_id)

    async def delete_workflow(self, workflow_id: int, company_id: int) -> str:
        """
        Deletes a workflow by its ID.

        Args:
            workflow_id: The ID of the workflow.
            company_id: The ID of the company the workflow belongs to.

        Returns:
            True if the workflow was deleted successfully, False otherwise.

        """
        res = await self.repo.delete_workflow(workflow_id, company_id)
        return "success" if res else "failed"

    async def update_workflow(
        self, workflow_id: int, company_id: int, data: CreateWorkflowModel
    ) -> str:
        """
        Updates an existing workflow.

        Args:
            workflow_id: The ID of the workflow.
            company_id: The ID of the company the workflow belongs to.
            data: The updated workflow data.

        Returns:
            True if the workflow was updated successfully, False otherwise.

        """
        # if workflow update then remove the cache for those jd's
        await self.redis.delete(f"jd_stage_automation_{workflow_id}")
        res = await self.repo.update_workflow(workflow_id, company_id, data)
        return "success" if res else "failed"
