from src.app.routers.scenarios.repositories.abstract_scenario_repo import AbstractScenarioRepo
from src.app.routers.scenarios.schemas.scenario_schemas import Create<PERSON>cenarioModel, ScenarioModel


class FakeScenarioRepo(AbstractScenarioRepo):
    async def create_scenario(
        self,
        data: CreateScenarioModel,
        company_id: int,
        created_by: int,
    ) -> ScenarioModel:
        return ScenarioModel(
            id=1,
            category=data.category,
            description=data.description,
        )

    async def get_scenarios(
        self,
        query: str,
        company_id: int,
        offset: int,
        limit: int,
    ) -> list[ScenarioModel]:
        return [
            ScenarioModel(
                id=1,
                category="Sample Category 1",
                description="Sample Description 1",
            ),
            ScenarioModel(
                id=2,
                category="Sample Category 2",
                description="Sample Description 2",
            ),
        ]

    async def update_scenario(
        self, scenario_id: int, company_id: int, data: CreateScenarioModel
    ) -> ScenarioModel | None:
        return ScenarioModel(
            id=1,
            category=data.category,
            description=data.description,
        )

    async def delete_scenario(self, scenario_id: int, company_id: int, created_by: int) -> bool:
        return True
