from abc import ABC, abstractmethod

from src.app.routers.scenarios.schemas.scenario_schemas import CreateScenarioModel, ScenarioModel


class AbstractScenarioRepo(ABC):
    """Abstract class for scenario repository"""

    @abstractmethod
    async def create_scenario(
        self,
        data: CreateScenarioModel,
        company_id: int,
        created_by: int,
    ) -> ScenarioModel:
        """Create scenario data in database

        Args:
            data (CreateScenarioModel): Data for creating scenario
            company_id (int): Company id
            created_by (int): User id who created scenario

        Returns:
            ScenarioModel: Scenario data
        """
        raise NotImplementedError

    @abstractmethod
    async def get_scenarios(
        self,
        query: str,
        company_id: int,
        offset: int,
        limit: int,
    ) -> list[ScenarioModel]:
        """Get scenarios from database

        Args:
            query (str): Search query
            company_id (int): Company id
            offset (int): Offset
            limit (int): Limit

        Returns:
            list[ScenarioModel]: Scenario list
        """
        raise NotImplementedError

    @abstractmethod
    async def update_scenario(
        self, scenario_id: int, company_id: int, data: CreateScenarioModel
    ) -> ScenarioModel | None:
        """Update scenario data in database

        Args:
            scenario_id (int): Scenario id
            company_id (int): Company id
            data (CreateScenarioModel): Data for updating scenario

        Returns:
            ScenarioModel | None: Scenario data or None
        """
        raise NotImplementedError

    @abstractmethod
    async def delete_scenario(self, scenario_id: int, company_id: int, created_by: int) -> bool:
        """Delete scenario from database

        Args:
            scenario_id (int): Scenario id
            company_id (int): Company id
            created_by (int): User id

        Returns:
            bool: True if scenario is deleted else False
        """
        raise NotImplementedError
