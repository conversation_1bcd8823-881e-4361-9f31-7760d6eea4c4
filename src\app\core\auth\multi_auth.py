from typing import TypeVar

from fastapi import Depends, Request, Response
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer

from src.app.core import logger
from src.app.core.exceptions.user_exception import UserAccountError
from src.app.utils.schemas import AuthType
from src.app.utils.schemas.user_schemas import AuthUserSchema

from .authentication import get_current_user
from .server_to_server_auth import authenticate_service_request


T = TypeVar("T", AuthUserSchema, bool)


class MultiAuthDependency:
    """
    Handles multiple authentication types for FastAPI endpoints.
    Supports user authentication, service authentication, or both.
    """

    def __init__(self, auth_type: AuthType = AuthType.USER) -> None:
        self.auth_type = auth_type
        self.auth_funcs = {
            AuthType.SERVICE: self._authenticate_service,
            AuthType.USER: self._authenticate_user,
            AuthType.BOTH: self._authenticate_both,
        }
        self._bearer = HTTPBearer(auto_error=False)

    async def __call__(
        self,
        request: Request,
        response: Response,
        credential: HTTPAuthorizationCredentials = Depends(HTTPBearer(auto_error=False)),
    ) -> AuthUserSchema | bool:
        """
        Authenticate the request based on the specified auth type.

        Returns:
            Union[AuthUserSchema, bool]: AuthUserSchema for user auth, bool for service auth

        Raises:
            UserAccountError: If authentication fails
        """
        try:
            auth_func = self.auth_funcs.get(self.auth_type)
            if not auth_func:
                raise UserAccountError(
                    message="Invalid authentication type", error_code="INVALID_AUTH_TYPE"
                )
            return await auth_func(request, response, credential)  # type: ignore
        except Exception as err:
            logger.exception("Authentication failed", exc_info=err)
            raise UserAccountError(message="Authentication failed", error_code="INVALID_TOKEN")

    async def _authenticate_user(
        self, request: Request, response: Response, credential: HTTPAuthorizationCredentials
    ) -> AuthUserSchema:
        """Handle user authentication"""
        return get_current_user(response, credential)

    async def _authenticate_service(self, request: Request) -> bool:
        """Handle service authentication"""
        return authenticate_service_request(request)  # type: ignore

    async def _authenticate_both(
        self, request: Request, response: Response, credential: HTTPAuthorizationCredentials
    ) -> AuthUserSchema | bool:
        """Try user authentication first, fall back to service authentication"""
        try:
            return await self._authenticate_user(request, response, credential)
        except UserAccountError:
            # If user auth fails, try service auth
            try:
                return await self._authenticate_service(request)
            except Exception as err:
                logger.exception("Service authentication failed", exc_info=err)
                raise UserAccountError(
                    message="Authentication failed for both user and service",
                    error_code="AUTH_FAILED",
                )
