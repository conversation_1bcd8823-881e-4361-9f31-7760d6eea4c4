from injector import singleton

from src.app.core.exceptions.resources_exceptions import InvalidOperationError
from src.app.db.tables.workflow_enums import (
    AutomationChannelType,
    AutomationEmailType,
    AutomationScheduleType,
)
from src.app.db.tables.workflow_tables import AutomationTable
from src.app.routers.scenarios.repositories.abstract_automation_repo import AbstractAutomationRepo
from src.app.routers.scenarios.schemas.automation_schemas import (
    AutomationModel,
    CreateAutomationModel,
)


@singleton
class AutomationRepo(AbstractAutomationRepo):
    """Repository class for Automation objects."""

    async def convert_table_to_model(self, table: AutomationTable) -> AutomationModel:
        """Convert AutomationTable object to AutomationModel object.

        Args:
            table (AutomationTable): The AutomationTable object.

        Returns:
            AutomationModel: The converted AutomationModel object.
        """
        return AutomationModel(
            automation_id=table.id,
            description=table.description,
            template_id=table.template_id,
            channel_type=AutomationChannelType(table.channel_type).name,
            email_type=AutomationEmailType(table.email_type).name if table.email_type else None,
            schedule_type=AutomationScheduleType(table.schedule_type).name,
            schedule_time=table.schedule_time,
        )

    async def create_automation(
        self, data: CreateAutomationModel, company_id: int, created_by: int, scenario_id: int
    ) -> AutomationModel:
        """Create a new Automation object.

        Args:
            data (CreateAutomationModel): The data for the new Automation object.
            company_id (int): The company ID.
            created_by (int): The ID of the user creating the Automation object.
            scenario_id (int): The scenario ID.

        Returns:
            AutomationModel: The newly created AutomationModel object.
        """
        table = AutomationTable(
            scenario_id=scenario_id,
            description=data.description,
            template_id=data.template_id,
            channel_type=AutomationChannelType[data.channel_type].value,
            email_type=AutomationEmailType[data.email_type].value if data.email_type else None,
            schedule_type=AutomationScheduleType[data.schedule_type].value,
            schedule_time=data.schedule_time,
        )
        table.company_id = company_id
        table.created_by = created_by
        await table.save()
        return await self.convert_table_to_model(table)

    async def get_by_scenario_id(
        self,
        scenario_id: int,
        offset: int,
        limit: int,
    ) -> list[AutomationModel]:
        """Get Automation objects by scenario ID.

        Args:
            scenario_id (int): The scenario ID.
            offset (int): The offset to start retrieving objects from.
            limit (int): The maximum number of objects to retrieve.

        Returns:
            list[AutomationModel]: A list of retrieved AutomationModel objects.
        """
        table = (
            await AutomationTable.filter(scenario_id=scenario_id, status=True)
            .offset(offset)
            .limit(limit)
        )
        return [await self.convert_table_to_model(row) for row in table]

    async def get_automation(self, automation_id: int, company_id: int) -> AutomationModel:
        """Get AutomationModel object by automation ID and company ID.

        Args:
            automation_id (int): The automation ID.
            company_id (int): The company ID.

        Returns:
            AutomationModel: The retrieved AutomationModel object.
        """
        table = await AutomationTable.get(id=automation_id, status=True)
        return await self.convert_table_to_model(table)

    async def delete_automation(self, automation_id: int, company_id: int) -> str:
        """Delete Automation object by automation ID and company ID.

        Args:
            automation_id (int): The automation ID.
            company_id (int): The company ID.

        Returns:
            str: "success" if the operation was successful, "failed" otherwise.
        """
        res = await AutomationTable.filter(id=automation_id, company_id=company_id).update(
            status=False
        )
        return "success" if res else "failed"

    async def update_automation(
        self, automation_id: int, company_id: int, scenario_id: int, data: CreateAutomationModel
    ) -> AutomationModel:
        """Update an existing Automation object.

        Args:
            automation_id (int): The ID of the Automation object to update.
            company_id (int): The company ID.
            scenario_id (int): The scenario ID.
            data (CreateAutomationModel): The updated data for the Automation object.

        Raises:
            InvalidOperationError: If the Automation object is not found.

        Returns:
            AutomationModel: The updated AutomationModel object.
        """
        # convert to string enums to int
        res = await AutomationTable.filter(
            id=automation_id, company_id=company_id, scenario_id=scenario_id
        ).update(
            description=data.description,
            template_id=data.template_id,
            channel_type=AutomationChannelType[data.channel_type].value,
            email_type=AutomationEmailType[data.email_type].value if data.email_type else None,
            schedule_type=AutomationScheduleType[data.schedule_type].value,
            schedule_time=data.schedule_time,
        )
        if not res:
            raise InvalidOperationError(message=f"Automation with id {automation_id} not found")
        return AutomationModel(**data.model_dump(), automation_id=automation_id)

    async def get_automations(
        self,
        automation_ids: list[int],
    ) -> list[AutomationModel]:
        """Get multiple AutomationModel objects by automation IDs and company ID.

        Args:
            automation_ids (list[int]): A list of automation IDs.

        Returns:
            list[AutomationModel]: A list of retrieved AutomationModel objects.
        """
        table = await AutomationTable.filter(id__in=automation_ids)
        return [await self.convert_table_to_model(row) for row in table]
