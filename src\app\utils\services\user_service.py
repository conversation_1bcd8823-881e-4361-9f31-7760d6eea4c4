from injector import inject, singleton

from src.app.routers.jds.schemas.user_schema import AssigneeSchema
from src.app.utils.repos.user_repo import UserRepository


@singleton
class UserService:
    """Provides methods for retrieving user data, utilizing a cache."""

    @inject
    def __init__(self, user_repository: UserRepository):
        """
        Initializes UserService.

        Args:
            user_repository: Repository for user data operations.
        """
        self.user_repository = user_repository

    async def get_user_by_ids(
        self, user_ids: int | list[str] | list[int]
    ) -> dict[int, AssigneeSchema]:
        """
        Retrieves user(s) by their ID(s).

        Args:
            user_ids: The ID, or a list of IDs, of the user(s) to retrieve.
            company_id: The ID of the company.

        Returns:
            A dictionary where keys are user IDs and values are AssigneeSchema objects,
        """
        if isinstance(user_ids, int):
            user_ids = [user_ids]  # type: ignore[list-item]
        found_ids, not_found_ids = await self.user_repository.get_cached_users(user_ids)

      
        print("===============+>", found_ids, not_found_ids)

        if not_found_ids:
            users = await self.user_repository.fetch_users_from_api(not_found_ids)
            await self.user_repository.update_user_cache(users)
            print("*****************", users)
            found_ids += users
        # Return a dictionary of users with IDs in user_ids
        return {int(user.id): user for user in found_ids}

