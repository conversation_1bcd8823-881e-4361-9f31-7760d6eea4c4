[run]
omit =
    src/tests/*
    src/app/core/*
    src/app/utils/publisher/*
    src/app/utils/schemas/*
    src/app/utils/repos/*
    src/app/routers/scenarios/repositories/*
    src/app/utils/*
    src/app/db/router.py
    src/app/tasks/jd_tasks.py
    src/app/routers/jds/repositories/jd_repo.py
    src/app/db/views/setup_views.py
    src/app/tasks/services/task_service.py
    src/app/routers/workflows/repoitories/workflow_repo.py
    src/app/routers/jds/repositories/queries.py
    src/app/routers/graphql/jd/service/query_service.py
    src/app/worker.py
    src/app/routers/jds/services/jd_service.py
    src/app/routers/jds/repositories/jd_workflow_repo.py
    src/app/routers/jds/repositories/abstract_jd_repo.py
    src/app/routers/graphql/context.py
    src/app/routers/jds/router.py

[pytest]
asyncio_mode = auto