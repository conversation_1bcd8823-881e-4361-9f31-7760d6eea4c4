from fastapi import APIRouter, Depends, Query, Request
from fastapi_injector import Injected

from src.app.core.auth.authentication import get_current_user
from src.app.core.auth.multi_auth import MultiAuthDependency
from src.app.core.auth.server_to_server_auth import authenticate_service_request
from src.app.routers.jds.schemas.apply_form_schemas import (
    ApplyFormJDModel,
    JDResponse,
    JDSearchResponseModel,
)
from src.app.routers.jds.schemas.jd_schemas import (
    CreateJDModel,
    JDEmbeddingModel,
    JDFullUpdateModel,
    JDModel,
    JDPartialUpdateModel,
    JDSearchQueryModel,
    JDStatusModel,
    LeaderBoardModel,
)
from src.app.routers.jds.schemas.jd_workflow_schemas import CreateJDWorkflowModel
from src.app.routers.jds.services.jd_service import JDService
from src.app.routers.jds.services.jd_workflow_service import JDWorkflowService
from src.app.routers.workflows.schemas.workflow_schemas import WorkflowModel
from src.app.utils.response_helper import success_response
from src.app.utils.schemas.user_schemas import AuthType, AuthUserSchema
from src.app.utils.swagger_helper import generate_swagger_responses


router = APIRouter(
    tags=["JD"],
)


@router.post("/v1/jds", responses=generate_swagger_responses(JDModel))
async def save_jd(
    request: Request,
    jd: CreateJDModel,
    service: JDService = Injected(JDService),
    user: AuthUserSchema = Depends(get_current_user),
):
    """Saves the given JD data to the repository."""
    jd_save_response = await service.create_jd(jd, user)
    return success_response(jd_save_response.model_dump(), request)


@router.get("/v1/jds/{jd_id}/embedding", responses=generate_swagger_responses(JDEmbeddingModel))
async def get_jd_embedding(
    request: Request,
    jd_id: str,
    service: JDService = Injected(JDService),
    user: AuthUserSchema = Depends(get_current_user),
):
    jd_embedding = await service.get_jd_embedding(int(jd_id))
    return success_response(jd_embedding, request)


@router.put("/v1/jds/{jd_id}", responses=generate_swagger_responses(str))
async def update_jd(
    request: Request,
    jd_id: str,
    jd_data: JDFullUpdateModel,
    service: JDService = Injected(JDService),
    user: AuthUserSchema = Depends(get_current_user),
):
    """
    Updates the given JD data in the repository.
    """

    jd_update_response = await service.update_jd(int(jd_id), jd_data, user)
    return success_response(jd_update_response, request)


@router.patch("/v1/jds/{jd_id}", responses=generate_swagger_responses(str))
async def patch_jd(
    request: Request,
    jd_id: str,
    jd_data: JDPartialUpdateModel,
    service: JDService = Injected(JDService),
    user: AuthUserSchema = Depends(get_current_user),
):
    """Update the application form for a job description.
    Only partial updates are allowed."""
    form_update_response = await service.patch_jd(int(jd_id), jd_data, user)
    return success_response(form_update_response, request)


@router.delete("/v1/jds/{jd_id}", responses=generate_swagger_responses(str))
async def delete_jd(
    request: Request,
    jd_id: str,
    service: JDService = Injected(JDService),
    user: AuthUserSchema = Depends(get_current_user),
):
    """
    Soft delete a JD by its ID.
    """
    jd_delete_response = await service.delete_jd(int(jd_id), user)
    return success_response(jd_delete_response, request)


@router.get("/v1/jds/{jd_id}/workflows", responses=generate_swagger_responses(WorkflowModel))
async def get_workflow(
    request: Request,
    jd_id: str,
    service: JDWorkflowService = Injected(JDWorkflowService),
    _: AuthUserSchema | tuple = Depends(MultiAuthDependency(AuthType.BOTH)),
):
    """
    Handles the application form submission for a job description.
    """
    jd_apply_response = await service.get_workflow_by_id(int(jd_id))
    return success_response(jd_apply_response, request)


@router.post("/v1/jds/{jd_id}/workflows", responses=generate_swagger_responses(WorkflowModel))
async def create_workflow(
    request: Request,
    jd_id: str,
    data: CreateJDWorkflowModel,
    service: JDWorkflowService = Injected(JDWorkflowService),
    user: AuthUserSchema = Depends(get_current_user),
):
    """
    Handles the application form submission for a job description.
    """
    jd_apply_response = await service.update_or_create(int(jd_id), data, int(user.user_id))
    return success_response(jd_apply_response, request)


###############################################################################
################ Internal endpoints  ##########################################
###############################################################################


@router.get("/v1/jds", responses=generate_swagger_responses(list[JDResponse]))
async def get_jds(
    request: Request,
    jd_ids: list[str] = Query(...),
    service: JDService = Injected(JDService),
    _: bool = Depends(authenticate_service_request),
):
    """Get jds by ids. For internal communication only"""
    jds_response = await service.get_jds(jd_ids)
    return success_response(jds_response, request)


@router.get("/v1/jds/assignees", responses=generate_swagger_responses(dict))
async def get_jds_assignees(
    request: Request,
    jd_ids: list[str] = Query(...),
    service: JDService = Injected(JDService),
    _: bool = Depends(authenticate_service_request),
):
    """Get jds assignees by jd_ids. For internal communication only"""
    response = await service.get_jds_assignees(jd_ids)
    return success_response(response, request)


@router.get("/v1/jds:search", responses=generate_swagger_responses(list[JDSearchResponseModel]))
async def search_jds(
    request: Request,
    query: JDSearchQueryModel = Depends(),
    service: JDService = Injected(JDService),
    _: bool = Depends(authenticate_service_request),
):
    """Get the jds based on search query. For internal communication only"""
    jds_response, total_records = await service.search_jds(query)
    return success_response(jds_response, request, metadata={"total_records": total_records})


###############################################################################
################ un-authenticated endpoints for external services #############
###############################################################################


@router.get("/v1/jds/{jd_id}:apply_form", responses=generate_swagger_responses(ApplyFormJDModel))
async def get_apply_form(request: Request, jd_id: str, service: JDService = Injected(JDService)):
    """
    Handles the application form submission for a job description.
    """
    jd_apply_response = await service.get_apply_form(int(jd_id))
    return success_response(jd_apply_response, request)


###############################################################################
################################ DASHBOARD ####################################
###############################################################################


@router.get("/v1/dashboard/jds", responses=generate_swagger_responses(JDStatusModel))
async def get_jds_for_dashboard(
    request: Request,
    service: JDService = Injected(JDService),
    user: AuthUserSchema = Depends(get_current_user),
):
    """Get jds for dashboard."""
    jds_response = await service.get_jds_for_dashboard(user.company)
    return success_response(jds_response, request)


@router.get(
    "/v1/dashboard/leaderboard",
    responses=generate_swagger_responses(list[LeaderBoardModel]),
    summary="Gets the leaderboard for the company",
)
async def get_leaderboard(
    request: Request,
    user: AuthUserSchema = Depends(get_current_user),
    service: JDService = Injected(JDService),
):
    result = await service.get_leaderboard(user.company)
    return success_response(result, request)
