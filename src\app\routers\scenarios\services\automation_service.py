from injector import inject, singleton

from src.app.routers.scenarios.repositories.automation_repo import AutomationRepo
from src.app.routers.scenarios.schemas.automation_schemas import (
    AutomationModel,
    CreateAutomationModel,
)


@singleton
class AutomationService:
    """
    A service class for managing automations.

    This class provides methods for interacting with automations, including:
        - Getting multiple automations by their IDs.
        - Creating new automations.
        - Updating existing automations.
        - Deleting automations.
        - Retrieving automations associated with a specific scenario ID.

    Attributes:
        repo (AutomationRepo): An instance of the AutomationRepo class for accessing
            automation data.

    """

    @inject
    def __init__(self, repo: AutomationRepo):
        """
        Initializes a new instance of the AutomationService class.

        Args:
            repo (AutomationRepo): An instance of the AutomationRepo class for accessing
                automation data.

        """
        self.repo = repo

    async def get_automations(self, automation_ids: list[int]) -> list[AutomationModel]:
        """
        Retrieves a list of automations based on their IDs.

        Args:
            automation_ids (list[int]): A list of automation IDs to retrieve.

        Returns:
            list[AutomationModel]: A list of AutomationModel objects representing the
                retrieved automations.

        """
        return await self.repo.get_automations(automation_ids)

    async def create_automation(
        self, data: CreateAutomationModel, company_id: int, created_by: int, scenario_id: int
    ) -> AutomationModel:
        """
        Creates a new automation.

        Args:
            data (CreateAutomationModel): The data for the new automation.
            company_id (int): The ID of the company the automation belongs to.
            created_by (int): The ID of the user who created the automation.
            scenario_id (int): The ID of the scenario the automation is associated with.

        Returns:
            AutomationModel: An AutomationModel object representing the newly created automation.

        """
        return await self.repo.create_automation(data, company_id, created_by, scenario_id)

    async def update_automation(
        self, automation_id: int, company_id: int, data: CreateAutomationModel, scenario_id: int
    ) -> AutomationModel:
        """
        Updates an existing automation.

        Args:
            automation_id (int): The ID of the automation to update.
            company_id (int): The ID of the company the automation belongs to.
            data (CreateAutomationModel): The updated data for the automation.
            scenario_id (int): The ID of the scenario the automation is associated with.

        Returns:
            AutomationModel: An AutomationModel object representing the updated automation.

        """
        return await self.repo.update_automation(automation_id, company_id, scenario_id, data)

    async def delete_automation(self, automation_id: int, company_id: int) -> str:
        """
        Deletes an automation.

        Args:
            automation_id (int): The ID of the automation to delete.
            company_id (int): The ID of the company the automation belongs to.

        Returns:
            str: A success message if the deletion was successful.

        """
        return await self.repo.delete_automation(automation_id, company_id)

    async def get_by_scenario_id(
        self,
        scenario_id: int,
        offset: int,
        limit: int,
    ) -> list[AutomationModel]:
        """
        Retrieves a list of automations associated with a specific scenario ID.

        Args:
            scenario_id (int): The ID of the scenario to retrieve automations for.
            offset (int): The starting offset for pagination.
            limit (int): The maximum number of automations to retrieve.

        Returns:
            list[AutomationModel]: A list of AutomationModel objects representing the
                retrieved automations.

        """
        return await self.repo.get_by_scenario_id(scenario_id, offset, limit)
