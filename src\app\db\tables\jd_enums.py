from enum import IntEnum, StrEnum


class StageStr(StrEnum):
    SOURCED = "SOURCED"
    APPLIED = "APPLIED"


class PriorityType(IntEnum):
    LOW = 1
    MEDIUM = 2
    HIGH = 3


class JobType(IntEnum):
    FULL_TIME = 1
    CONTRACT = 2
    INTERNSHIP = 3


class PresenceType(IntEnum):
    REMOTE = 1
    ON_SITE = 2
    HYBRID = 3


class SalaryDurationType(IntEnum):
    HOURLY = 1
    DAILY = 2
    WEEKLY = 3
    MONTHLY = 4
    YEARLY = 5


class DepartmentType(IntEnum):
    HR = 1
    FINANCE = 2
    IT = 3
    MARKETING = 4
    SALES = 5
    OTHER = 6


class JDStatusType(IntEnum):
    DRAFT = 1
    ACTIVE = 2
    CLOSED = 3
    ARCHIVED = 4


class JDPostType(IntEnum):
    LINKEDIN_POST = 1
    LINKEDIN_GROUP = 2
