import pytest
from httpx import AsyncClient

from src.app.core.auth.server_to_server_auth import ServerToServerAuth
from src.app.main import app
from src.tests.integration_tests.test_main import client, override_current_user


test_user = override_current_user()
auth_service = ServerToServerAuth()


@pytest.mark.asyncio
async def test_post_workflow():
    data = {
        "name": "Sample Workflow",
        "workflow": {"stage": [{"automations": [1, 2, 3], "name": "stage"}], "version": 1},
    }
    response = client.post("/v1/workflows", json=data)
    assert response.status_code == 201
    assert response.json()["data"]["name"] == "Sample Workflow"
    assert response.json()["data"]["workflow"] == {"stage": [], "version": 1}


@pytest.mark.asyncio
async def test_get_workflow_by_id():
    workflow_id = 1
    response = client.get(f"/v1/workflows/{workflow_id}")
    assert response.status_code == 200
    assert response.json()["data"]["id"] == workflow_id
    assert response.json()["data"]["name"] == "Sample Workflow"
    assert response.json()["data"]["workflow"] == {"stage": [], "version": 1}


@pytest.mark.asyncio
async def test_delete_workflow_success():
    workflow_id = 1
    response = client.delete(f"/v1/workflows/{workflow_id}")
    assert response.status_code == 200
    assert response.json()["data"] == "success"


@pytest.mark.asyncio
async def test_delete_workflow_fail():
    workflow_id = 1
    response = client.delete(f"/v1/workflows/{workflow_id}")
    assert response.status_code == 200
    assert response.json()["data"] == "success"


@pytest.mark.skip(reason="Api takes too long to run")
async def test_put_workflow():
    workflow_id = 1
    data = {
        "name": "Workflow Updated",
        "workflow": {"stage": [{"automations": [1, 2, 3], "name": "stage"}], "version": 1},
    }

    async with AsyncClient(app=app, base_url="http://testserver") as client:
        response = await client.put(f"/v1/workflows/{workflow_id}", json=data)
        assert response.status_code == 200
        assert response.json()["data"] == "success"


@pytest.mark.asyncio
async def test_get_workflow():
    response = client.get("/v1/workflows")
    data = response.json()["data"]
    expected = [
        {
            "id": 1,
            "name": "Sample Workflow",
            "workflow": {"stage": [], "version": 1},
            "automations": [],
        }
    ]
    assert response.status_code == 200
    assert data == expected


@pytest.mark.asyncio
async def test_post_workflow_validation():
    data = {}
    response = client.post("/v1/workflows", json=data)
    assert response.status_code == 422
    assert response.json()["error"]["code"] == "VALIDATION_ERROR"


@pytest.mark.asyncio
async def test_get_workflow_by_id_validation():
    workflow_id = "Jack"
    response = client.get(f"/v1/workflows/{workflow_id}")
    assert response.status_code == 422
    assert response.json()["error"]["code"] == "VALIDATION_ERROR"


@pytest.mark.asyncio
async def test_delete_workflow_validation():
    workflow_id = "Jack"
    response = client.delete(f"/v1/workflows/{workflow_id}")

    assert response.status_code == 422
    assert response.json()["error"]["code"] == "VALIDATION_ERROR"


@pytest.mark.asyncio
async def test_update_workflow_validation():
    workflow_id = "abc"
    data = {}
    response = client.put(f"/v1/workflows/{workflow_id}", json=data)
    assert response.status_code == 422
    assert response.json()["error"]["code"] == "VALIDATION_ERROR"


@pytest.mark.asyncio
async def test_get_workflow_validation():
    workflow_id = "abc"
    data = {}
    response = client.put(f"/v1/workflows/{workflow_id}", json=data)
    assert response.status_code == 422
    assert response.json()["error"]["code"] == "VALIDATION_ERROR"
