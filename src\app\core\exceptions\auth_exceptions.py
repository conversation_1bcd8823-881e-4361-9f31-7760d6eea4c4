from src.app.core.exceptions.general_exception import GeneralException


class NoAuthTokenError(GeneralException):
    def __init__(
        self,
        message="No authentication token provided",
        error_code="NO_AUTH_TOKEN",
    ):
        super().__init__(
            status_code=401,
            message=message,
            error_code=error_code,
        )


class InvalidAuthTokenError(GeneralException):
    def __init__(
        self,
        message="Invalid authentication token provided",
        error_code="INVALID_AUTH_TOKEN",
    ):
        super().__init__(
            message=message,
            status_code=401,
            error_code=error_code,
        )
