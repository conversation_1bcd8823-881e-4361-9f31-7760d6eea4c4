from injector import inject, singleton

from src.app.db.tables.workflow_tables import WorkflowJDTable
from src.app.routers.jds.repositories.abstract_jd_workflow import AbstractJDWorkflowRepo
from src.app.routers.jds.schemas.jd_workflow_schemas import CreateJDWorkflowModel
from src.app.routers.workflows.repoitories.workflow_repo import WorkflowRepo
from src.app.routers.workflows.schemas.workflow_schemas import WorkflowModel


@singleton
class JDWorkflowRepo(AbstractJDWorkflowRepo):
    @inject
    def __init__(self, workflow_repo: WorkflowRepo):
        self.workflow_repo = workflow_repo

    async def update_or_create(
        self, jd_id: int, data: CreateJDWorkflowModel, user_id: int
    ) -> WorkflowModel:
        """
        Update or create a workflow for a specific JD (job description) ID.

        Args:
            jd_id (int): The ID of the JD.
            data (CreateJDWorkflowModel): The data containing the workflow details.
            user_id (int): The ID of the user who created or updated the workflow.

        Returns:
            WorkflowModel: The updated or newly created workflow model.
        """
        # Check workflow exits or not
        if not await self.workflow_repo.check_workflow_exists(data.workflow_id):
            raise ValueError(f"Workflow with id {data.workflow_id} not found")

        res = await WorkflowJDTable.exists(jd_id=jd_id)
        if not res:
            table = await WorkflowJDTable.create(
                workflow_id=data.workflow_id,
                jd_id=jd_id,
                local_workflow=data.workflow,
                last_updated_by=user_id,
            )
        else:
            await WorkflowJDTable.filter(jd_id=jd_id).update(
                workflow_id=data.workflow_id, local_workflow=data.workflow, last_updated_by=user_id
            )
            # Fetch the updated object
            table = await WorkflowJDTable.get(workflow_id=data.workflow_id, jd_id=jd_id)
        return WorkflowModel(id=table.workflow_id, workflow=table.local_workflow)

    async def get_workflow_by_id(self, jd_id: int) -> WorkflowModel:
        """
        Retrieve a workflow by its associated JD (job description) ID.

        Args:
            jd_id (int): The ID of the JD.

        Returns:
            WorkflowModel: The workflow model associated with the provided JD ID.
        """
        table = (
            await WorkflowJDTable.get(jd_id=jd_id)
            .select_related("workflow_base")
            .values(
                "workflow__id",  # workflow_id from WorkflowTable
                "workflow__name",  # name from WorkflowTable
                "workflow__workflow",  # workflow from WorkflowTable
                "local_workflow",  # local_workflow from WorkflowJDTable
                "modified_at",  # modified_at from WorkflowJDTable (TimestampMixin),
                "last_updated_by",
            )
        )

        workflow = table["local_workflow"]
        if workflow is None:
            workflow = table["workflow__workflow"]

        return WorkflowModel(
            id=table["workflow__id"],
            name=table["workflow__name"],
            workflow=workflow,
            modified_at=table["modified_at"],
            last_updated_by_id=table["last_updated_by"],
        )

    async def get_workflows_by_jd_ids(self, jd_ids: list[int]) -> dict[int, dict]:
        """
        Retrieve workflows associated with a list of JD (job description) IDs.

        Args:
            jd_ids (list[int]): A list of JD IDs to retrieve workflows for.

        Returns:
            dict[int, dict]: A dictionary mapping each JD ID to its corresponding
            workflow data. If a local workflow exists, it is returned; otherwise,
            the base workflow is returned.
        """
        tables = (
            await WorkflowJDTable.filter(jd_id__in=jd_ids)
            .select_related("workflow_base")
            .values(
                "jd_id",
                "local_workflow",
                "workflow__workflow",  # workflow from WorkflowTable
            )
        )

        if not tables:
            return {}

        return {
            i["jd_id"]: i["local_workflow"] if i["local_workflow"] else i["workflow__workflow"]
            for i in tables
        }
