from abc import ABC, abstractmethod

from src.app.routers.graphql.jd.schemas.jd_schema import <PERSON>DFilters, JDSort, JDTaskStatusEnum
from src.app.routers.jds.schemas.apply_form_schemas import (
    ApplyFormJDModel,
    JDResponse,
    JDSearchResponseModel,
)
from src.app.routers.jds.schemas.jd_schemas import (
    CreateJDModel,
    JDFullUpdateModel,
    JDModel,
    JDPartialUpdateModel,
    JDSearchQueryModel,
    JDStatusModel,
    LeaderBoardModel,
)
from src.app.utils.schemas.user_schemas import AuthUserSchema


class AbstractJDRepo(ABC):
    @abstractmethod
    async def get_jd_count_today(self, company_id: int) -> int:
        raise NotImplementedError

    @abstractmethod
    async def convert_to_table(self, jd: JDModel) -> dict:
        raise NotImplementedError

    @abstractmethod
    async def check_jd_user_access(self, jd_id: int, user_id: int) -> bool:
        raise NotImplementedError

    @abstractmethod
    async def is_ready_to_save(self, jd_data: CreateJDModel, user_id: int) -> bool:
        raise NotImplementedError

    @abstractmethod
    async def create_jd_with_assignee(self, jd_data: JDModel) -> None:
        raise NotImplementedError

    @abstractmethod
    async def create_jd(self, jd_data: JDModel) -> JDModel:
        raise NotImplementedError

    @abstractmethod
    async def get_jds(self, jd_ids: list[int]) -> list[JDResponse]:
        raise NotImplementedError

    @abstractmethod
    async def apply_filters_jd(self, user: AuthUserSchema, filters: JDFilters | None) -> str:
        raise NotImplementedError

    @abstractmethod
    async def filter_by_assignees(
        self, user: AuthUserSchema, assignees: list[JDTaskStatusEnum]
    ) -> str:
        raise NotImplementedError

    @abstractmethod
    async def update_jd(self, jd_id: int, jd_data: JDModel) -> JDModel:
        raise NotImplementedError

    @abstractmethod
    async def update_jd_assignee(
        self, jd_id: int, jd_data: JDFullUpdateModel, company_id: int, user_id: int
    ) -> tuple[list[int], list[int]]:
        raise NotImplementedError

    @abstractmethod
    async def update_jd_embedding(self, jd_id: int, embedding: dict) -> bool:
        raise NotImplementedError

    @abstractmethod
    async def get_jds_gql(
        self,
        user: AuthUserSchema,
        offset: int,
        limit: int,
        jd_ids: list | None = None,
        fields: list[str] | None = None,
        filters: JDFilters | None = None,
        sort_by: JDSort | None = None,
    ) -> tuple[list[dict], int]:
        raise NotImplementedError

    @abstractmethod
    async def get_jd_assignees(self, jd_ids: list[int]) -> list[dict]:
        raise NotImplementedError

    @abstractmethod
    async def search_jds(
        self, search_query: JDSearchQueryModel
    ) -> tuple[list[JDSearchResponseModel], int]:
        raise NotImplementedError

    @abstractmethod
    async def delete_jd(self, jd_id: int) -> bool:
        raise NotImplementedError

    @abstractmethod
    async def crate_assign_users(
        self,
        assigned_by: int,
        jd_id: int,
        assignee_ids: list[str],
        company_id: int,
    ):
        raise NotImplementedError

    @abstractmethod
    async def get_application_form(self, jd_id: int) -> ApplyFormJDModel:
        raise NotImplementedError

    @abstractmethod
    async def update_application_form(self, jd_id: int, jd_data: JDPartialUpdateModel) -> bool:
        raise NotImplementedError

    @abstractmethod
    async def update_career_form(self, jd_id: int, jd_data: JDPartialUpdateModel) -> bool:
        raise NotImplementedError

    @abstractmethod
    async def update_jd_status(self, jd_id: int, jd_data: JDPartialUpdateModel) -> bool:
        raise NotImplementedError

    @abstractmethod
    async def withdraw_from_jd(
        self, jd_id: int, jd_data: JDPartialUpdateModel, user_id: int, company_id: int
    ) -> bool:
        raise NotImplementedError

    @abstractmethod
    async def get_jd_embedding(self, jd_id: int, fields: list | None = None) -> dict:
        raise NotImplementedError

    @abstractmethod
    async def get_jd_by_assignees(self, user_id: int) -> list[int]:
        raise NotImplementedError

    @abstractmethod
    async def get_jds_for_dashboard(self, company_id: int) -> JDStatusModel:
        raise NotImplementedError

    @abstractmethod
    async def get_leaderboard(self, company_id: int) -> list[LeaderBoardModel]:
        raise NotImplementedError
