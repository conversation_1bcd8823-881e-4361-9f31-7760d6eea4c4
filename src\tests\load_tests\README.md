# JD API Load Testing with Locust

This directory contains load tests for the Job Description (JD) API using Locust.

## 🚀 Quick Start

### 1. Install Dependencies
```bash
pip install -r src/tests/load_tests/requirements.txt
```

### 2. Configure Test Settings
Before running tests, update the following in `test_jd_load.py`:

```python
# Update login credentials (line ~25)
login_data = {
    "username": "<EMAIL>",  # Replace with actual test user
    "password": "your_test_password"           # Replace with actual test password
}

# Update assignee IDs (line ~75)
"assignee_ids": [1, 2],  # Replace with actual test user IDs
```

### 3. Run Tests

#### Option A: Automated Test Suite
```bash
# Linux/Mac
chmod +x src/tests/load_tests/run_jd_tests.sh
./src/tests/load_tests/run_jd_tests.sh

# Windows
src\tests\load_tests\run_jd_tests.bat
```

#### Option B: Manual Test Execution
```bash
# Basic test with web UI
locust -f src/tests/load_tests/test_jd_load.py --host=http://localhost:8000

# Headless test
locust -f src/tests/load_tests/test_jd_load.py \
       --host=http://localhost:8000 \
       --users=10 --spawn-rate=2 --run-time=5m \
       --headless --html=reports/jd_test.html
```

## 📊 Test Scenarios

The load test includes the following scenarios:

### 1. **Successful JD Creation** (Weight: 3)
- Creates JDs with complete, valid data
- Tests normal API flow
- Validates response format

### 2. **Minimal Data JD Creation** (Weight: 1)
- Tests with only required fields
- Validates minimum viable payload

### 3. **Invalid Data Testing** (Weight: 1)
- Tests error handling with invalid data
- Ensures proper validation responses

## 🔧 Configuration

### Environment Variables
```bash
export TEST_HOST=http://localhost:8000  # API base URL
```

### Test Data Customization
Edit `generate_jd_payload()` method in `test_jd_load.py` to customize:
- Job titles and descriptions
- Skills combinations
- Company information
- Salary ranges

## 📈 Understanding Results

### Key Metrics to Monitor:
- **Response Time**: Should be < 2000ms for JD creation
- **Success Rate**: Should be > 99%
- **Throughput**: Requests per second
- **Error Rate**: Should be < 1%

### Performance Thresholds:
- ✅ **Good**: Response time < 1500ms, Success rate > 99%
- ⚠️ **Warning**: Response time 1500-3000ms, Success rate 95-99%
- ❌ **Critical**: Response time > 3000ms, Success rate < 95%

## 🐛 Troubleshooting

### Common Issues:

#### 1. Authentication Failures
```
❌ Login failed: 401 - Unauthorized
```
**Solution**: Update login credentials in `test_jd_load.py`

#### 2. Permission Errors
```
❌ Permission denied - check user permissions
```
**Solution**: Ensure test user has JD creation permissions

#### 3. Validation Errors
```
❌ Validation error: assignee_ids field required
```
**Solution**: Update assignee_ids with valid test user IDs

#### 4. Connection Errors
```
❌ Connection refused
```
**Solution**: Ensure your API server is running on the specified host

## 📁 File Structure

```
src/tests/load_tests/
├── test_jd_load.py          # Main Locust test file
├── run_jd_tests.sh          # Linux/Mac test runner
├── run_jd_tests.bat         # Windows test runner
├── requirements.txt         # Python dependencies
├── README.md               # This file
└── reports/                # Generated test reports (created automatically)
    ├── *.html              # HTML reports with charts
    ├── *_stats.csv         # Detailed statistics
    └── *.log               # Test execution logs
```

## 🎯 Next Steps

1. **Customize Test Data**: Modify `generate_jd_payload()` for your specific use case
2. **Add More Endpoints**: Extend tests to cover other JD APIs (search, update, delete)
3. **Performance Tuning**: Use results to identify and fix bottlenecks
4. **CI/CD Integration**: Add these tests to your deployment pipeline

## 📞 Support

If you encounter issues:
1. Check the generated log files in `reports/` directory
2. Verify your API server is running and accessible
3. Ensure test credentials have proper permissions
4. Review the Locust documentation: https://docs.locust.io/
