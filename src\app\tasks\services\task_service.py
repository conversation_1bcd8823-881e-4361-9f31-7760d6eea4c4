import asyncio

from injector import inject, singleton
from redis.asyncio import <PERSON><PERSON>
from snowflakekit import SnowflakeGenerator

from src.app.core import logger
from src.app.routers.jds.repositories.jd_repo import J<PERSON>epo
from src.app.routers.jds.schemas.jd_schemas import JDFullUpdateModel
from src.app.routers.jds.schemas.user_schema import AssigneeSchema
from src.app.utils.helpers.time_conversion import convert_to_days
from src.app.utils.pubsub.notification_center import NotificationType
from src.app.utils.pubsub.publisher import NotificationPublisher
from src.app.utils.repos.ai_repo import AIRepo
from src.app.utils.repos.candidate_repo import CandidateRepository
from src.app.utils.schemas.user_schemas import AuthUserSchema
from src.app.utils.services.user_service import UserService


@singleton
class TaskService:
    @inject
    def __init__(
        self,
        jd_repo: <PERSON><PERSON><PERSON><PERSON>,
        sid: SnowflakeGenerator,
        redis: Redis,
        publisher: NotificationPublisher,
        user_service: UserService,
        ai_repo: AIRepo,
        candidate_repo: CandidateRepository,
    ):
        """Initializes the JDService class with the provided JDRepository
           instance.

        Args:
            jd_repo (JDRepo): An instance of the JDRepository class.

        Returns:
            None
        """
        self.jd_repo = jd_repo
        self.sid = sid
        self.redis = redis
        self.publisher = publisher
        self.user_service = user_service
        self.ai_repo = ai_repo
        self.candidate_repo = candidate_repo

    async def generate_jd_embedding(self, data: dict) -> dict:
        text = f"""
        Designation: {data.get("designation", "")}
        Skills: {data.get("primary_skills", "") + (data.get("secondary_skills", "") or "")}
        """
        embedding = await self.ai_repo.fetch_embedding_from_api(text) or {}
        res = await self.jd_repo.update_jd_embedding(int(data["jd_id"]), embedding)
        # update this to track
        embedding.update({"embedding": [], "jd_id": data["jd_id"], "status": res})
        return embedding

    async def run_ranking(self, jd_id: int):
        """
        Runs the ranking process for a given job description ID.

        Args:
            jd_id (int): The ID of the job description to run ranking for.

        Returns:
            The result of the ranking process.
        """
        return await self.candidate_repo.update_ranking(jd_id=jd_id)

    async def notify_assignees_modification(
        self,
        data: JDFullUpdateModel,
        jd_id: int,
        current_user: AuthUserSchema,
        assignee_ids: list[int],
        unassigned_ids: list[int],
    ):
        # send summary notification to current user
        user_details: dict[int, AssigneeSchema] = await self.user_service.get_user_by_ids(
            assignee_ids + unassigned_ids
        )

        assigned_users = [
            user.model_dump() for user in user_details.values() if user.id in assignee_ids
        ]
        unassigned = [
            user.model_dump() for user in user_details.values() if user.id in unassigned_ids
        ]
        # Publish the parent notification
        sid = await self.sid.generate()
        meta = {"assigned": assigned_users, "unassigned": unassigned}
        await self.publisher.publish_notification(
            NotificationType.ASSIGNEE_UPDATE,
            id=sid,
            user_id=current_user.user_id,
            user_name=current_user.name,
            company_id=current_user.company,
            jd_id=str(jd_id),
            jd_name=data.jd_name,
            meta=meta,
        )
        # send notifications to assigned
        notification_task = []
        if assigned_users:
            notification_task.extend(
                [
                    self.publisher.publish_notification(
                        NotificationType.ASSIGNEE_CHANGE,
                        id=await self.sid.generate(),
                        parent_notification_id=sid,
                        user_id=user.get("id", 0),
                        user_name=user.get("user_name", ""),
                        company_id=current_user.company,
                        jd_id=str(jd_id),
                        jd_name=data.jd_name,
                        action="Assigned",
                    )
                    for user in assigned_users
                ]
            )

        # send notifications to un-assigned
        if unassigned:
            notification_task.extend(
                [
                    self.publisher.publish_notification(
                        NotificationType.ASSIGNEE_CHANGE,
                        id=await self.sid.generate(),
                        parent_notification_id=sid,
                        user_id=user.get("id", 0),
                        user_name=user.get("user_name", ""),
                        company_id=current_user.company,
                        jd_id=str(jd_id),
                        jd_name=data.jd_name,
                        action="Unassigned",
                    )
                    for user in unassigned
                ]
            )

        await asyncio.gather(*notification_task)

        logger.info(f"JD {jd_id} assignees updated by {current_user.name}", extra={"meta": meta})

    async def update_jd(
        self, jd_id: int, data: JDFullUpdateModel | dict, current_user: AuthUserSchema
    ) -> bool:
        async_tasks = []
        if isinstance(data, dict):
            data = JDFullUpdateModel(**data)
        # must update the user_id as assignees ids bigInt to db_id int
        # get the int user_id assignees
        assignees = await self.user_service.get_user_by_ids(data.assignee_ids)
        data.assignee_ids = [str(assignee.id) for _, assignee in assignees.items()]

        # update notice periods
        data.notice_period_in_days = await convert_to_days(data.notice_period)
        # must remove jd cache
        await self.redis.delete(str(f"jd_{jd_id}"))
        await self.redis.delete(str(f"jd_assignees_{jd_id}"))
        jd_data = data.model_dump()
        jd_data.update({"jd_id": jd_id})
        async_tasks.extend(
            [
                self.jd_repo.update_jd(
                    jd_id=jd_id,
                    jd_data=data,
                ),
                self.generate_jd_embedding(jd_data),
            ]
        )

        await asyncio.gather(*async_tasks)

        assignee_ids, unassigned_ids = await self.jd_repo.update_jd_assignee(
            jd_id=int(jd_id),
            jd_data=data,
            company_id=current_user.company,
            user_id=current_user.user_id,
        )

        if assignee_ids or unassigned_ids:  # only notify if there is any change on assignees
            await self.notify_assignees_modification(
                data, jd_id, current_user, assignee_ids, unassigned_ids
            )

        return True
