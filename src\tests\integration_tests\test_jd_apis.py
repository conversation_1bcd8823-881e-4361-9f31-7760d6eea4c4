import pytest
from httpx import AsyncClient

from src.app.core.auth.server_to_server_auth import ServerToServerAuth
from src.tests.integration_tests.test_main import client, override_current_user
from src.app.main import app

test_user = override_current_user()
auth_service = ServerToServerAuth()
import redis.asyncio as aioredis


@pytest.fixture(scope="function")
async def redis_client():
    client = aioredis.from_url("redis://localhost")
    yield client
    await client.close()
@pytest.mark.skip(reason="Skipping due to Redis dependency")
async def test_post_jd_save(redis_client):
    jd_data = {
    "jd_name": "Lead Python Developer",
    "jd_status": "DRAFT",
    "assignee_ids": ["1001", "1002"],
    "jd_text": "Looking for an experienced Python developer to lead the backend team.",
    "hiring_priority": "HIGH",
    "designation": "Senior Backend Engineer",
    "min_work_exp": 5,
    "max_work_exp": 10,
    "job_type": "FULL_TIME",
    "job_preference": "REMOTE",
    "primary_skills": "Python, FastAPI, Docker",
    "secondary_skills": "Kubernetes, PostgreSQL",
    "preferred_location": "Remote",
    "no_of_positions": 3,
    "company_name": "TechNova Inc.",
    "client_name": "Nova Clients LLC",
    "company_url": "https://technova.io",
    "department": "IT",  
    "notice_period": "4 weeks",
    "salary_currency": "USD",
    "min_salary": 8000.0,
    "max_salary": 12000.0,
    "salary_duration": "MONTHLY",
    "hourly_rate": "100 USD/hr",
    "confirmed_start_date": "2024-09-01T10:00:00",
    "bill_to_client": "Nova Clients LLC",
    "contract_acceptance": True,
    "recommendation_skills": "Python, FastAPI, Cloud",
    "recommendation_keywords": "microservices, async, REST",
    "recommendation_companies": "Google, Amazon, Meta",
    "recommendation_industries": "Tech, SaaS, Cloud",
    "recommendation_colleges": "MIT, Stanford, IIT",
    "targets_shortlisted": 5,
    "targets_shortlisted_date": "2024-08-15T10:00:00",
    "targets_hiring_date": "2024-09-30T10:00:00",
    "tax_term": "W2",
    "vendor_email": "<EMAIL>",
    "vendor_phone": "******-999-1234",
    "vendor_link": "https://technova.io/partners"
}    
    response = client.post("/v1/jds/", json=jd_data)
    assert response.status_code == 200
    assert "jd_id" in response.json()["data"]


@pytest.mark.asyncio
async def test_get_jds():
    jd_id = "3923697286803099648"
    headers = {"Authorization": f"Bearer {auth_service.generate_token()}"}
    response = client.get(f"/v1/jds?jd_ids={jd_id}", headers=headers)
    assert response.status_code == 200
    assert "data" in response.json()
    assert len(response.json()["data"]) == 1


@pytest.mark.asyncio
async def test_patch_jd():
    jd_id = 3927105638430150656
    form_data = {
        "action": "JD_STATUS",
        "application_form": {},
        "career_page": {},
        "jd_status": "ACTIVE",
        "comment": "string",
    }
    response = client.patch(f"/v1/jds/{jd_id}", json=form_data)
    assert response.status_code == 200
    assert response.json()["data"] == "Successful"


@pytest.mark.asyncio
async def test_jd_delete():
    jd_id = 3927105638430150656
    response = client.delete(f"/v1/jds/{jd_id}")
    assert response.status_code == 200
    assert response.json()["data"] == "Deleted"


@pytest.mark.asyncio
async def test_jd_get_apply_form():
    jd_id = 3926569310093316096
    response = client.get(f"/v1/jds/{jd_id}:apply_form")
    assert response.status_code == 200
    assert response.json()["data"]["jd_name"] == "Software Engineer"


@pytest.mark.asyncio
async def test_post_jd_save_validation():
    jd_data = {}
    response = client.post("/v1/jds", json=jd_data)
    assert response.status_code == 422
    assert response.json()["error"]["code"] == "VALIDATION_ERROR"


@pytest.mark.asyncio
async def test_get_jd_embedding_validation():
    jd_id = "Jack"
    response = client.get(f"/v1/jds/{jd_id}/embedding")
    assert response.status_code == 422
    assert response.json()["error"]["code"] == "VALIDATION_ERROR"


@pytest.mark.asyncio
async def test_get_jds_validation():
    jd_id = "Jack"
    headers = {"Authorization": f"Bearer {auth_service.generate_token()}"}
    response = client.get(f"/v1/jds?jd_ids={jd_id}", headers=headers)
    assert response.status_code == 422
    assert response.json()["error"]["code"] == "VALIDATION_ERROR"


@pytest.mark.asyncio
async def test_put_jd_save_validation():
    jd_id = "abc"
    jd_data = {}
    response = client.put(f"/v1/jds/{jd_id}", json=jd_data)
    assert response.status_code == 422
    assert response.json()["error"]["code"] == "VALIDATION_ERROR"


@pytest.mark.asyncio
async def test_patch_jd_validation():
    jd_id = 1
    form_data = {}
    response = client.patch(f"/v1/jds/{jd_id}", json=form_data)
    assert response.status_code == 422
    assert response.json()["error"]["code"] == "VALIDATION_ERROR"


@pytest.mark.asyncio
async def test_jd_delete_validation():
    jd_id = "Jack"
    response = client.delete(f"/v1/jds/{jd_id}")
    assert response.status_code == 422
    assert response.json()["error"]["code"] == "VALIDATION_ERROR"


@pytest.mark.asyncio
async def test_jd_get_apply_form_validation():
    jd_id = "Jack"
    response = client.get(f"/v1/jds/{jd_id}:apply_form")
    assert response.status_code == 422
    assert response.json()["error"]["code"] == "VALIDATION_ERROR"


@pytest.mark.asyncio
async def test_post_create_workflow_validation():
    jd_id = "12312"
    jd_data = {}
    headers = {"Authorization": f"Bearer {auth_service.generate_token()}"}
    response = client.post(f"/v1/jds/{jd_id}/workflows", json=jd_data, headers=headers)
    assert response.status_code == 422
    assert response.json()["error"]["code"] == "VALIDATION_ERROR"
