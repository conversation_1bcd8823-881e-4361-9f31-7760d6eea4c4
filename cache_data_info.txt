1.  cache key : jd_stage_<jd_id>
    description : The jd workflow stages with candidate counts fetched from candidate service
    used_files: src/app/utils/repository/candidate_repo.py
    given_ex_time : 1 hour
    time to leave : whenever a candidate added or candidate stage modified to that particular jd
                    or changes in the workflow of that jd
    source_service: candidate_service

2. cache key : jd_stage_automation<jd_id>
    description : The jd workflow stages with with related automations
    given_ex_time : 1 hour
    time to leave : whenever a candidate added or candidate stage modified to that particular jd
                    or changes in the workflow of that jd
    source_service: candidate_service