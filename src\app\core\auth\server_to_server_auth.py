import jwt
from fastapi import Request
from injector import singleton

from src.app.core import logger
from src.app.core.config.settings import get_settings
from src.app.core.exceptions.auth_exceptions import InvalidAuthTokenError, NoAuthTokenError


@singleton
class ServerToServerAuth:
    """
    This handle server-to-server authentication using JWT.

    This class provides methods to generate and authenticate JWT tokens
    based on the application's settings.

    Attributes:
        key (str): The secret key used to encode and decode JWT tokens.
        algorithm (str): The algorithm used for encoding and decoding JWT tokens.
        identifier (str): The identifier used in the JWT payload to verify authenticity.
    """

    def __init__(self):
        settings = get_settings()
        self.key = settings.JWT_SECRET_KEY
        self.algorithm = settings.JWT_ALGORITHM
        self.identifier = settings.JWT_IDENTIFIER

    def generate_token(self, payload: dict | None = None) -> str:
        """
        Generate a JWT token.

        Returns:
            str: A JWT token encoded with the configured secret key and algorithm.
        """
        data = {"identifier": self.identifier}
        if payload:
            data.update(payload)
        return jwt.encode(
            payload=data,
            key=self.key,
            algorithm=self.algorithm,
        )

    def authenticate(self, request: Request):
        """
        Authenticate a request using the JWT token in the Authorization header.

        Args:
            request (Request): The incoming request object containing headers.

        Raises:
            NoAuthTokenError: If no Authorization header is present in the request.
            InvalidAuthTokenError: If the token is invalid or the payload does not match.

        Returns:
            bool: True if authentication is successful.
        """

        # return True, "Random"

        auth_header = request.headers.get("Authorization")
        if not auth_header:
            logger.exception("No auth token provided")
            raise NoAuthTokenError()

        try:
            # Split "Bearer <token>"
            token = auth_header.split(" ")[1]
            decoded = jwt.decode(
                jwt=token,
                key=self.key,
                algorithms=self.algorithm,
            )
        except Exception:
            logger.exception("Invalid auth token")
            raise InvalidAuthTokenError()

        identifier = decoded.get("identifier")
        if identifier != self.identifier:
            logger.exception("Invalid auth token")
            raise InvalidAuthTokenError("Token with invalid payload")

        return True, decoded


def authenticate_service_request(request: Request) -> tuple[bool, dict]:
    return ServerToServerAuth().authenticate(request)
