from fastapi import APIRouter, Depends, Query, Request
from fastapi_injector import Injected

from src.app.core.auth.authentication import get_current_user
from src.app.routers.scenarios.schemas.automation_schemas import (
    AutomationModel,
    CreateAutomationModel,
)
from src.app.routers.scenarios.schemas.scenario_schemas import CreateScenarioModel, ScenarioModel
from src.app.routers.scenarios.services.automation_service import AutomationService
from src.app.routers.scenarios.services.scenario_service import ScenarioService
from src.app.utils.response_helper import success_response
from src.app.utils.schemas.user_schemas import AuthUserSchema
from src.app.utils.swagger_helper import generate_swagger_responses


router = APIRouter(
    tags=["scenarios"],
)


@router.post("/v1/scenarios", responses=generate_swagger_responses(ScenarioModel))
async def save_scenario(
    request: Request,
    data: CreateScenarioModel,
    service: ScenarioService = Injected(ScenarioService),
    user: AuthUserSchema = Depends(get_current_user),
):
    """Saves the given JD data to the repository."""
    jd_save_response = await service.create_scenario(
        data=data,
        company_id=user.company,
        created_by=int(int(user.user_id)),
    )
    return success_response(jd_save_response, request)


@router.get("/v1/scenarios", responses=generate_swagger_responses(ScenarioModel))
async def get_scenarios(
    request: Request,
    query: str | None = None,
    offset: int = 0,
    limit: int = 10,
    service: ScenarioService = Injected(ScenarioService),
    user: AuthUserSchema = Depends(get_current_user),
):
    """Get all scenarios."""
    scenarios = await service.get_scenarios(
        query=query, company_id=user.company, offset=offset, limit=limit
    )
    return success_response(scenarios, request)


# @router.get("/v1/scenarios/{scenario_id}", responses=generate_swagger_responses(ScenarioModel))
# async def get_scenario_by_id(
#         request: Request,
#         scenario_id: int,
#         service: ScenarioService = Injected(ScenarioService),
#         user: AuthUserSchema = Depends(get_current_user),
# ):
#     """Get scenario by id."""
#     scenario = await service.get_scenario_by_id(
#         scenario_id=scenario_id,
#         company_id=user.company_id
#     )
#     return success_response(scenario, request)


@router.delete("/v1/scenarios/{scenario_id}", responses=generate_swagger_responses(str))
async def delete_scenario(
    request: Request,
    scenario_id: int,
    service: ScenarioService = Injected(ScenarioService),
    user: AuthUserSchema = Depends(get_current_user),
):
    """Delete scenario by id."""
    jd_delete_response = await service.delete_scenario(
        scenario_id=scenario_id, company_id=user.company, created_by=int(int(user.user_id))
    )
    return success_response(jd_delete_response, request)


@router.put("/v1/scenarios/{scenario_id}", responses=generate_swagger_responses(ScenarioModel))
async def update_scenario(
    request: Request,
    scenario_id: int,
    data: CreateScenarioModel,
    service: ScenarioService = Injected(ScenarioService),
    user: AuthUserSchema = Depends(get_current_user),
):
    """Update scenario by id."""
    jd_update_response = await service.update_scenario(
        scenario_id=scenario_id, data=data, company_id=user.company
    )
    return success_response(jd_update_response, request)


@router.post(
    "/v1/scenarios/{scenario_id}/automations", responses=generate_swagger_responses(AutomationModel)
)
async def create_automations(
    request: Request,
    scenario_id: int,
    data: CreateAutomationModel,
    service: AutomationService = Injected(AutomationService),
    user: AuthUserSchema = Depends(get_current_user),
):
    """Update scenario by id."""
    res = await service.create_automation(
        data=data, company_id=user.company, created_by=int(user.user_id), scenario_id=scenario_id
    )
    return success_response(res, request)


@router.get(
    "/v1/scenarios/{scenario_id}/automations",
    responses=generate_swagger_responses(AutomationModel),
)
async def get_automations(
    request: Request,
    scenario_id: int,
    offset: int = 0,
    limit: int = 10,
    service: AutomationService = Injected(AutomationService),
    _=Depends(get_current_user),
):
    """Get all automations."""
    automations = await service.get_by_scenario_id(
        scenario_id=scenario_id, offset=offset, limit=limit
    )
    return success_response(automations, request)


@router.delete(
    "/v1/scenarios/{scenario_id}/automations/{automation_id}",
    responses=generate_swagger_responses(str),
)
async def delete_automation_by_id(
    request: Request,
    scenario_id: int,
    automation_id: int,
    service: AutomationService = Injected(AutomationService),
    user: AuthUserSchema = Depends(get_current_user),
):
    """Delete automation by id."""
    jd_delete_response = await service.delete_automation(
        automation_id=automation_id, company_id=user.company
    )
    return success_response(jd_delete_response, request)


@router.put(
    "/v1/scenarios/{scenario_id}/automations/{automation_id}",
    responses=generate_swagger_responses(AutomationModel),
    description="""
    Update automation by id. Make sure to pass automation_id and scenario_id.
    """,
)
async def update_automation_by_id(
    request: Request,
    scenario_id: int,
    automation_id: int,
    data: CreateAutomationModel,
    service: AutomationService = Injected(AutomationService),
    user: AuthUserSchema = Depends(get_current_user),
):
    """Update automation by id."""
    jd_update_response = await service.update_automation(
        automation_id=automation_id, company_id=user.company, data=data, scenario_id=scenario_id
    )
    return success_response(jd_update_response, request)


@router.get(
    "/v1/automations",
    responses=generate_swagger_responses(list[AutomationModel]),
    description="""
        Get automation data by id, this will be used by workflow executor
    """,
)
async def get_automations_ids(
    request: Request,
    automation_id: list[int] = Query([]),
    service: AutomationService = Injected(AutomationService),
    _=Depends(get_current_user),
):
    """Get automation data by id."""
    automation_data = await service.get_automations(
        automation_ids=automation_id,
    )
    return success_response(automation_data, request)
